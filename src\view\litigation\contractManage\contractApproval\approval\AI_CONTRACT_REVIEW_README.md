# AI合同审查功能实现说明

## 功能概述

在新增合同审批单页面的左侧添加了"AI合同审查"按钮，点击后弹出AI合同审查弹窗，展示合同审查功能。

## 实现内容

### 1. 主要文件修改

#### ContractApprovalMain.vue
- 在标题行左侧添加了"AI合同审查"按钮
- 添加了弹窗组件和相关的数据、方法
- 导入了新创建的AIContractReview组件

#### AIContractReview.vue (新建)
- 将原htsc.html页面转换为Vue组件
- 保持了原有的所有功能和样式
- 使用showdown库进行Markdown渲染
- 保持了原有的接口请求方式不变

### 2. 功能特性

#### 左侧区域 - 合同文本显示
- 显示解析后的合同文本
- 支持文本选择和高亮匹配
- 提供"提交"按钮进行AI审查

#### 右侧区域 - 审查结果显示
- 三种立场切换：全部立场、甲方立场、乙方立场
- Markdown格式的审查结果展示
- 支持HTML渲染显示

#### 交互功能
- 文本选择高亮匹配
- 匹配结果导航（上一个/下一个）
- 加载状态显示
- 错误提示处理

### 3. 接口保持不变

#### 文本获取接口
```javascript
POST http://*************:5004/mobilemode/api/htsc/htsc02/api_hgsc
参数: { "fjid": urlParams.get("fjid") }
```

#### AI审查接口
```javascript
POST http://*************:5001/v1/chat/completions
参数: {
  "messages": [{"role": "user", "content": plainText}],
  "shencha_moshi": shencha_moshi // quan/jia/yi
}
```

### 4. 依赖安装

已安装showdown依赖用于Markdown渲染：
```bash
npm install showdown
```

### 5. 样式保持

完全保持了原htsc.html中的所有CSS样式，确保视觉效果一致。

## 使用方法

1. 在合同审批页面点击左上角的"AI合同审查"按钮
2. 弹窗打开后会自动加载合同文本
3. 点击"提交"按钮进行AI审查
4. 可以切换不同立场查看审查结果
5. 支持文本选择和高亮匹配功能

## 测试页面

创建了test-ai-review.html测试页面，可以独立测试AI合同审查功能的界面和交互效果。

## 技术栈

- Vue.js 2.x
- Element UI
- Showdown (Markdown渲染)
- 原生JavaScript (文本处理)
- CSS3 (样式和动画)

## 注意事项

1. 接口地址和参数格式保持与原HTML页面完全一致
2. 所有样式和交互效果都已完整迁移
3. 弹窗大小设置为90%宽度，适应不同屏幕尺寸
4. 保持了原有的错误处理和用户提示机制

# 真实数据格式处理实现

## 🎯 数据格式说明

根据您提供的真实数据，`mainData.textList` 的数据结构如下：

```javascript
[
  {
    "docId": "19799DBF1CBF47F79A523E19A05A4EB5",
    "name": "sojrSalesContract_A1A3_WAC25070050.pdf", 
    "status": "sojrSalesContract_A1A3_WAC25070050.pdf"
  }
]
```

## ✨ 实现的适配功能

### 1. 数据格式解析适配

#### 🔧 更新的 availableFiles 计算属性
```javascript
// 在AIContractReview.vue中
availableFiles() {
  const files = [];
  if (this.contractFiles && this.contractFiles.length > 0) {
    this.contractFiles.forEach((item, index) => {
      if (item) {
        // 处理新的数据格式：直接包含docId, name, status的对象
        if (item.docId && item.name) {
          files.push({
            name: item.name,
            id: item.docId,        // 使用docId作为文件ID
            status: item.status,
            originalIndex: index,
            data: item
          });
        }
        // 兼容旧的attachment格式（向下兼容）
        else if (item.attachment) {
          // ... 原有的attachment处理逻辑
        }
      }
    });
  }
  return files;
}
```

### 2. 文件ID处理优化

#### 🔗 docId 字段映射
```javascript
// 获取文件时使用docId
const selectedFile = this.availableFiles[this.selectedFileIndex];
const docId = selectedFile.id; // 这里是从docId字段映射过来的

// 调用UploadDoc API
docApi.download(docId).then(response => {
  // 处理文件内容
});

// 调用原有接口
const raw = JSON.stringify({
  "fjid": docId  // 将docId作为fjid参数传递
});
```

### 3. 接口参数适配

#### 📡 原有接口调用适配
```javascript
fetchComplianceTextFromOriginalAPI(docId) {
  const myHeaders = new Headers();
  myHeaders.append("Content-Type", "application/json");

  // 使用docId作为fjid参数
  const raw = JSON.stringify({
    "fjid": docId  // 真实数据中的docId字段
  });

  const requestOptions = {
    method: 'POST',
    headers: myHeaders,
    body: raw,
    redirect: 'follow'
  };

  fetch("http://*************:5004/mobilemode/api/htsc/htsc02/api_hgsc", requestOptions)
    .then(response => response.json())
    .then(result => {
      const wenbeng = result.wenben;
      this.form.complianceText = wenbeng;
      this.isLoading = false;
    });
}
```

## 🎨 用户界面适配

### 1. 文件信息显示

#### 📋 文件选择器优化
```vue
<el-select v-model="selectedFileIndex" @change="onFileChange">
  <el-option v-for="(file, index) in availableFiles" :key="index" :label="file.name" :value="index">
    <span style="float: left">{{ file.name }}</span>
    <span style="float: right; color: #8492a6; font-size: 13px">
      {{ file.id.substring(0, 8) }}...
    </span>
  </el-option>
</el-select>
```

#### 🏷️ 文件状态显示
```vue
<div class="header-right">
  <el-tag v-if="currentFileInfo" size="small" type="success">
    {{ currentFileInfo }}
  </el-tag>
  <el-tag size="small" type="info">
    {{ availableFiles.length }} 个文件
  </el-tag>
</div>
```

### 2. 调试信息增强

#### 🔍 控制台日志
```javascript
console.log('选中的文件信息:', selectedFile);
console.log('使用的docId:', docId);
console.log('解析后的可用文件:', files);
```

## 📊 数据处理流程

### 完整的处理流程
```
mainData.textList (真实数据)
    ↓
[{docId, name, status}, ...]
    ↓
availableFiles 计算属性解析
    ↓
[{name, id: docId, status, ...}, ...]
    ↓
用户选择文件
    ↓
获取 docId
    ↓
调用 UploadDoc API 或原有接口
    ↓
显示文本内容
```

### 数据字段映射
| 原始字段 | 映射字段 | 用途 |
|----------|----------|------|
| `docId` | `id` | 文件唯一标识符 |
| `name` | `name` | 文件显示名称 |
| `status` | `status` | 文件状态信息 |

## 🔧 兼容性处理

### 1. 向下兼容
```javascript
// 同时支持新旧两种数据格式
if (item.docId && item.name) {
  // 处理新格式：{docId, name, status}
  files.push({
    name: item.name,
    id: item.docId,
    status: item.status
  });
} else if (item.attachment) {
  // 处理旧格式：{attachment: "JSON字符串"}
  const attachmentData = JSON.parse(item.attachment);
  // ... 原有处理逻辑
}
```

### 2. 错误处理增强
```javascript
// 增加更详细的错误信息和调试日志
.catch(error => {
  console.error('原有接口调用失败:', error);
  console.log('使用的docId:', docId);
  console.log('请求参数:', raw);
  this.$message.error('获取文本失败，请检查网络或接口');
});
```

## 🧪 测试验证

### 1. 测试数据
```javascript
// 真实数据格式测试用例
const realDataFormat = [
  {
    "docId": "19799DBF1CBF47F79A523E19A05A4EB5",
    "name": "sojrSalesContract_A1A3_WAC25070050.pdf",
    "status": "sojrSalesContract_A1A3_WAC25070050.pdf"
  },
  {
    "docId": "28899CBF2DBF58G89B634F29B16B5FC6", 
    "name": "技术开发协议_补充条款.docx",
    "status": "技术开发协议_补充条款.docx"
  }
];
```

### 2. 测试覆盖
- ✅ 真实数据格式解析
- ✅ docId字段提取
- ✅ 文件名显示
- ✅ 多文件选择
- ✅ 接口参数传递
- ✅ 错误处理机制

## 🎯 关键改进点

### 1. 数据结构适配
- **直接支持**: 新的 `{docId, name, status}` 格式
- **字段映射**: `docId` → `id` 用于统一处理
- **向下兼容**: 保持对旧 `attachment` 格式的支持

### 2. 接口调用优化
- **参数适配**: 使用 `docId` 作为 `fjid` 参数
- **调试增强**: 添加详细的控制台日志
- **错误处理**: 更友好的错误提示和调试信息

### 3. 用户体验提升
- **文件信息**: 显示文件名和docId前缀
- **状态反馈**: 清晰的处理状态提示
- **多文件支持**: 完善的文件选择和切换功能

## 🚀 部署说明

### 使用方法
1. 确保 `mainData.textList` 包含正确的数据格式
2. 数据应包含 `docId`、`name`、`status` 字段
3. 点击"AI合同审查"按钮打开弹窗
4. 系统自动解析文件列表并显示
5. 选择要分析的文件进行AI审查

### 配置要求
- docId 必须是有效的文档标识符
- name 字段用于显示文件名
- status 字段用于显示文件状态
- 确保相关API接口可正常访问

## 📋 总结

通过这次适配，AI合同审查功能现在完全支持真实的 `mainData.textList` 数据格式：

✅ **完美适配**: 支持 `{docId, name, status}` 数据结构
✅ **向下兼容**: 保持对旧格式的支持
✅ **智能处理**: 自动识别和解析不同数据格式
✅ **用户友好**: 清晰的文件信息显示和操作界面
✅ **稳定可靠**: 完善的错误处理和调试信息

现在系统可以无缝处理您提供的真实数据格式，为用户提供流畅的合同审查体验！

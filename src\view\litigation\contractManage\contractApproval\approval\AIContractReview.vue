<template>
  <div class="compliance-page">
    <!-- 加载遮罩 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <div class="loading-text">加载中...</div>
    </div>
    
    <el-row :gutter="20">
      <!-- 左侧：合同文本显示区域 -->
      <el-col :span="12">
        <el-form ref="form" :model="form" label-width="120px">
          <div style="padding-left: 10px;">
            <div id="markdown-preview" class="markdown-preview">{{ complianceTextWithDefault }}</div>
            <el-row :gutter="10">
              <el-col :span="8" style="padding-top: 3px; text-align: right;">
                <el-button type="success" @click="submitForm">提交</el-button>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </el-col>
      
      <!-- 右侧：审查结果显示区域 -->
      <el-col :span="12">
        <div>
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <el-radio-group v-model="activeParty" class="full-width-radio-group">
                <el-radio-button label="all" class="full-width-radio-button">全部立场</el-radio-button>
                <el-radio-button label="partyA" class="full-width-radio-button">甲方立场</el-radio-button>
                <el-radio-button label="partyB" class="full-width-radio-button">乙方立场</el-radio-button>
              </el-radio-group>
            </div>
            <div class="markdown-container" @mouseup="handleTextSelection">
              <div id="markdown-result" class="markdown-content" v-html="renderedMarkdown"></div>
            </div>
          </el-card>
        </div>
      </el-col>
    </el-row>
    
    <!-- 匹配导航器 -->
    <div v-if="matches.length > 0" class="match-navigator">
      <el-button @click="scrollToPrevMatch">上一个</el-button>
      <span>{{ currentMatch + 1 }}/{{ matches.length }}</span>
      <el-button @click="scrollToNextMatch">下一个</el-button>
    </div>
  </div>
</template>

<script>
import showdown from 'showdown'

export default {
  name: 'AIContractReview',
  props: {
    contractFiles: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      form: { 
        complianceText: "" 
      },
      matches: [],
      currentMatch: 0,
      uploadedFile: null,
      activeParty: 'all',
      response: { 
        all: `## 
- **点击提交**: 获取审查内容
- **划词匹配**: 支持文本高亮和匹配功能

### 示例操作步骤

1. 点击"提交"按钮以获取审查内容。
2. 使用鼠标选中需要匹配的文本，系统将自动高亮并显示匹配结果。`, 
        partyA: `## 
- **点击提交**: 获取审查内容
- **划词匹配**: 支持文本高亮和匹配功能

### 示例操作步骤

1. 点击"提交"按钮以获取审查内容。
2. 使用鼠标选中需要匹配的文本，系统将自动高亮并显示匹配结果。`, 
        partyB: `## 
- **点击提交**: 获取审查内容
- **划词匹配**: 支持文本高亮和匹配功能

### 示例操作步骤

1. 点击"提交"按钮以获取审查内容。
2. 使用鼠标选中需要匹配的文本，系统将自动高亮并显示匹配结果。`, 
      },
      fjid: null, // 文件id
      isLoading: false // 加载状态
    }
  },
  computed: {
    complianceTextWithDefault() {
      return this.form.complianceText || '解析文本中......';
    },
    renderedMarkdown() {
      const converter = new showdown.Converter();
      return converter.makeHtml(this.response[this.activeParty]);
    }
  },
  mounted() {
    this.fetchComplianceText(); // 页面加载时调用接口获取文本
  },
  methods: {
    // 调用接口获取文本
    fetchComplianceText() {
      this.isLoading = true; // 开始加载
      const urlParams = new URLSearchParams(window.location.search);
      const myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      const raw = JSON.stringify({
        "fjid": urlParams.get("fjid")
      });

      const requestOptions = {
        method: 'POST',
        headers: myHeaders,
        body: raw,
        redirect: 'follow'
      };

      fetch("http://172.16.31.234:5004/mobilemode/api/htsc/htsc02/api_hgsc", requestOptions)
        .then(response => response.json())  // 将响应解析为 JSON
        .then(result => {
          // 直接使用 result.wenben 获取 wenben 字段的值
          const wenbeng = result.wenben;
          
          // 将接口返回的文本赋值给 complianceText
          this.form.complianceText = wenbeng;
          this.isLoading = false; // 结束加载
        })
        .catch(error => {
          console.error('error', error);
          this.$message.error('获取文本失败，请检查网络或接口');
          this.isLoading = false; // 结束加载
        });
    },

    // 添加导航方法
    scrollToNextMatch() {
      const nextIndex = (this.currentMatch + 1) % this.matches.length;
      this.scrollToMatch(nextIndex);
    },
    scrollToPrevMatch() {
      const prevIndex = (this.currentMatch - 1 + this.matches.length) % this.matches.length;
      this.scrollToMatch(prevIndex);
    },
    
    submitForm() {
      // 检查 complianceText 是否为空
      if (!this.form.complianceText.trim()) {
        this.$message.error('解析失败，请检查文本是否为空');
        return;
      }

      // 创建 Headers 对象
      this.isLoading = true; // 开始加载
      const myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");

      // 将 <p> 和 </p> 标签替换为换行符
      let plainText = this.form.complianceText
        .replace(/(\r\n|\n|\r)/gm, '\n') // 统一换行符为 \n
        .replace(/^\s+|\s+$/gm, '');

      // 替换连续的换行符为一个换行符
      plainText = plainText.replace(/\n+/g, '\n');

      // 根据 activeParty 设置 shencha_moshi
      let shencha_moshi = '';
      if (this.activeParty === 'all') {
        shencha_moshi = 'quan';
      } else if (this.activeParty === 'partyA') {
        shencha_moshi = 'jia';
      } else if (this.activeParty === 'partyB') {
        shencha_moshi = 'yi';
      }

      // 准备请求体
      const raw = JSON.stringify({
        "messages": [
          {
            "role": "user",
            "content": plainText,
          }
        ],
        "shencha_moshi": shencha_moshi
      });

      // 请求选项
      const requestOptions = {
        method: 'POST',
        headers: myHeaders,
        body: raw,
        redirect: 'follow'
      };

      // 发起请求
      fetch("http://172.16.31.234:5001/v1/chat/completions", requestOptions)
        .then(response => response.json())
        .then(data => {
          if (data.choices && data.choices.length > 0) {
            const message = data.choices[0].message.content;
            // 根据 activeParty 更新 response
            if (this.activeParty === 'all') {
              this.response.all = message;
            } else if (this.activeParty === 'partyA') {
              this.response.partyA = message;
            } else if (this.activeParty === 'partyB') {
              this.response.partyB = message;
            }
          } else {
            console.error('API 响应中没有 choices 数据');
            this.$message.error('API 响应中没有找到有效的回复');
          }
          this.isLoading = false; // 结束加载
        })
        .catch(error => {
          console.error('请求失败', error);
          this.$message.error('请求失败，请检查网络或接口');
          this.isLoading = false; // 结束加载
        });
    },
    
    handleTextSelection() {
      const selection = window.getSelection();
      const selectedText = selection.toString().trim();

      if (selectedText) {
        // 使用存储的文件内容进行匹配
        const content = this.form.complianceText;

        // 清除旧高亮和匹配记录
        const leftContent = document.querySelector('.markdown-preview');
        this.clearExistingHighlights(leftContent);
        this.matches = [];
        this.currentMatch = 0;

        // 创建正则表达式（处理特殊字符）
        const escapedText = selectedText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const regex = new RegExp(`(${escapedText})`, 'g');

        // 使用文档片段批量操作
        const fragment = document.createDocumentFragment();
        let lastIndex = 0;
        let match;

        while ((match = regex.exec(content)) !== null) {
          const startPos = match.index;
          const endPos = startPos + match[0].length;

          // 添加前半部分文本（包含换行）
          fragment.appendChild(
            document.createTextNode(content.slice(lastIndex, startPos))
          );

          // 创建高亮节点
          const highlightNode = document.createElement('mark');
          highlightNode.textContent = match[0];
          fragment.appendChild(highlightNode);

          // 记录匹配项
          this.matches.push(highlightNode);

          lastIndex = regex.lastIndex;
        }

        // 添加剩余文本（包含换行）
        fragment.appendChild(
          document.createTextNode(content.slice(lastIndex))
        );

        // 清空 markdown-preview 内容并添加新的片段
        leftContent.innerHTML = '';
        leftContent.appendChild(fragment);

        // 显示匹配信息
        if (this.matches.length > 0) {
          this.$message({
            message: `找到 ${this.matches.length} 处匹配`,
            type: 'info',
            duration: 3000
          });

          // 滚动到第一个匹配项
          this.scrollToMatch(0);

          // 5秒后淡出高亮
          setTimeout(() => {
            this.matches.forEach(node => {
              node.style.backgroundColor = 'transparent';
            });
          }, 5000);
        }
      }
    },

    // 辅助方法：清除现有高亮
    clearExistingHighlights(container) {
      const highlights = container.querySelectorAll('mark');
      highlights.forEach(highlight => {
        const text = highlight.textContent;
        const textNode = document.createTextNode(text);
        highlight.parentNode.replaceChild(textNode, highlight);
      });

      // 合并相邻的文本节点
      this.mergeAdjacentTextNodes(container);
    },

    // 辅助方法：合并相邻的文本节点
    mergeAdjacentTextNodes(element) {
      let node = element.firstChild;
      while (node && node.nextSibling) {
        if (node.nodeType === Node.TEXT_NODE &&
            node.nextSibling.nodeType === Node.TEXT_NODE) {

          const endsWithNewline = /\n$/.test(node.nodeValue);
          const startsWithNewline = /^\n/.test(node.nextSibling.nodeValue);

          // 仅合并不包含换行的相邻节点
          if (!endsWithNewline && !startsWithNewline) {
            node.nodeValue += node.nextSibling.nodeValue;
            node.parentNode.removeChild(node.nextSibling);
          } else {
            node = node.nextSibling;
          }
        } else {
          node = node.nextSibling;
        }
      }
    },
    
    // 新增辅助方法
    scrollToMatch(index) {
      const highlightColor = '#ff000033';
      if (index >= 0 && index < this.matches.length) {
        this.currentMatch = index;
        this.matches[index].scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });

        // 添加临时高亮效果
        this.matches.forEach(node => node.style.backgroundColor = highlightColor);
        this.matches[index].style.backgroundColor = '#ff0000aa';

        // 触发动画
        this.matches[index].classList.add('text-highlight');
      }
    }
  }
}
</script>

<style scoped>
/* 原样式内容保留 */
.compliance-page {
  padding-top: 10px;
}

.box-card {
  width: 100%;
}

.markdown-content {
  padding: 3px;
  border: 1px solid #f9f9f9;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.markdown-container {
  height: calc(100vh - 110px);
  overflow-y: auto;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #f9f9f9;
  padding: 10px;
}

.markdown-preview {
  margin-top: 12px;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #f9f9f9;
  overflow-y: auto;
  height: calc(100vh - 110px);
  white-space: pre-line;
}

.markdown-preview p {
  margin: 5px 0;
  padding: 3px;
  transition: background-color 0.3s;
}

.text-highlight {
  transition: background-color 0.3s;
  border-radius: 3px;
  padding: 0 2px;
}

.clearfix {
  padding: 0 !important;
}

.full-width-radio-group {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.full-width-radio-button {
  flex: 1;
  text-align: center;
  margin: 0 !important;
}

.el-radio-button .el-radio-button__inner {
  width: 100%;
  padding: 12px 20px;
  border-radius: 0 !important;
}

.el-radio-button:first-child .el-radio-button__inner {
  border-top-left-radius: 4px !important;
  border-bottom-left-radius: 4px !important;
}

.el-radio-button:last-child .el-radio-button__inner {
  border-top-right-radius: 4px !important;
  border-bottom-right-radius: 4px !important;
}

.el-card .el-card__header {
  padding: 0px;
}

.el-card .el-card__body {
  padding: 0px;
}

.match-navigator {
  position: fixed;
  right: 20px;
  bottom: 20px;
  background: rgba(255, 255, 255, 0.9);
  padding: 8px 15px;
  border-radius: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 2000;
}

@keyframes highlight-pulse {
  0% { background-color: #ff0000aa; }
  50% { background-color: #ff000066; }
  100% { background-color: #ff000033; }
}

.text-highlight {
  animation: highlight-pulse 1.5s ease-in-out;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  z-index: 9999;
}

.loading-spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #409eff;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 10px;
  font-size: 16px;
  color: #409eff;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.el-col-8 {
  width: 100%;
}
</style>

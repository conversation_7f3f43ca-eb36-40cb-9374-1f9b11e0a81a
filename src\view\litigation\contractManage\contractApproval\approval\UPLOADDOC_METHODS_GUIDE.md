# UploadDoc组件方法使用指南

## 🎯 概述

本指南详细介绍如何在AI合同审查功能中使用UploadDoc组件的各种方法来获取和处理文本附件。

## 📚 UploadDoc组件主要方法

### 1. 文件下载方法

#### `docApi.download(docId)`
```javascript
// 下载文件并获取blob数据
downloadCurrentFile() {
  const docId = selectedFile.id;
  
  docApi.download(docId).then(response => {
    // response.data 是文件的blob数据
    const blob = new Blob([response.data]);
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  });
}
```

### 2. 文件预览方法

#### `docApi.getFilePath(docId, orgId)`
```javascript
// 获取文件预览路径
previewCurrentFile() {
  const docId = selectedFile.id;
  const orgId = this.getCurrentOrgId();
  
  const previewPath = docApi.getFilePath(docId, orgId);
  // 在新窗口中打开预览
  window.open(previewPath, '_blank');
}
```

### 3. 文件编辑方法

#### `docApi.getEditFilePath(docId, orgId)`
```javascript
// 获取文件编辑路径
editCurrentFile() {
  const docId = selectedFile.id;
  const orgId = this.getCurrentOrgId();
  
  const editPath = docApi.getEditFilePath(docId, orgId);
  window.open(editPath, '_blank');
}
```

### 4. PDF转换方法

#### `docApi.convertToPDF(docId)`
```javascript
// 将文件转换为PDF
convertCurrentFileToPDF() {
  const docId = selectedFile.id;
  
  docApi.convertToPDF(docId).then(response => {
    console.log('PDF转换成功:', response);
    // 处理转换后的PDF文件
  }).catch(error => {
    console.error('PDF转换失败:', error);
  });
}
```

## 🔧 在AI合同审查中的实际应用

### 1. 文本提取流程

```javascript
// 完整的文本提取方法
fetchComplianceTextFromUploadDoc() {
  this.isLoading = true;
  
  const selectedFile = this.availableFiles[this.selectedFileIndex];
  const docId = selectedFile.id;
  
  // 1. 首先尝试下载文件
  docApi.download(docId).then(response => {
    const blob = response.data;
    const fileName = selectedFile.name;
    
    // 2. 根据文件类型处理
    this.extractTextFromBlob(blob, fileName, docId);
    
  }).catch(error => {
    // 3. 如果失败，使用原有接口作为备用
    this.fetchComplianceTextFromOriginalAPI(docId);
  });
}
```

### 2. 文件类型处理

```javascript
// 根据文件类型提取文本
extractTextFromBlob(blob, fileName, docId) {
  const fileExtension = fileName.split('.').pop().toLowerCase();
  
  switch(fileExtension) {
    case 'txt':
      this.readTextFile(blob);
      break;
    case 'pdf':
      // PDF文件使用原有接口解析
      this.fetchComplianceTextFromOriginalAPI(docId);
      break;
    case 'doc':
    case 'docx':
      // Word文件使用原有接口解析
      this.fetchComplianceTextFromOriginalAPI(docId);
      break;
    default:
      // 其他文件类型尝试原有接口
      this.fetchComplianceTextFromOriginalAPI(docId);
  }
}
```

### 3. 文本文件读取

```javascript
// 读取纯文本文件
readTextFile(blob) {
  const reader = new FileReader();
  
  reader.onload = (e) => {
    this.form.complianceText = e.target.result;
    this.isLoading = false;
    this.$message.success('文本内容加载成功');
  };
  
  reader.onerror = () => {
    this.$message.error('读取文件内容失败');
    this.isLoading = false;
  };
  
  reader.readAsText(blob, 'UTF-8');
}
```

## 🎨 用户界面集成

### 1. 操作按钮组

```vue
<div class="upload-doc-methods">
  <el-button-group>
    <el-button
      size="small"
      @click="downloadCurrentFile"
      :disabled="!currentFileInfo"
      icon="el-icon-download">
      下载文件
    </el-button>
    <el-button
      size="small"
      @click="previewCurrentFile"
      :disabled="!currentFileInfo"
      icon="el-icon-view">
      预览文件
    </el-button>
    <el-button
      size="small"
      @click="convertCurrentFileToPDF"
      :disabled="!currentFileInfo"
      icon="el-icon-document">
      转换PDF
    </el-button>
  </el-button-group>
</div>
```

### 2. 文件信息显示

```vue
<div class="header-right">
  <el-tag v-if="currentFileInfo" size="small" type="success">
    {{ currentFileInfo }}
  </el-tag>
  <el-tag size="small" type="info">
    {{ form.complianceText ? form.complianceText.length : 0 }} 字符
  </el-tag>
</div>
```

## 📋 完整的方法列表

### docApi 可用方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `download(docId)` | docId: 文件ID | Promise<blob> | 下载文件获取blob数据 |
| `getFilePath(docId, orgId)` | docId: 文件ID, orgId: 组织ID | String | 获取文件预览路径 |
| `getEditFilePath(docId, orgId)` | docId: 文件ID, orgId: 组织ID | String | 获取文件编辑路径 |
| `convertToPDF(docId)` | docId: 文件ID | Promise | 转换文件为PDF |
| `uploadFile(file, orgId)` | file: 文件对象, orgId: 组织ID | Promise | 上传文件 |
| `deleteFile(docId)` | docId: 文件ID | Promise | 删除文件 |

### 辅助方法

| 方法名 | 说明 |
|--------|------|
| `getCurrentOrgId()` | 获取当前组织ID |
| `extractTextFromBlob()` | 从blob中提取文本 |
| `readTextFile()` | 读取文本文件内容 |
| `fetchComplianceTextFromOriginalAPI()` | 使用原有接口获取文本 |

## 🔍 使用场景

### 1. 直接文本文件
- 使用 `download()` 获取文件内容
- 使用 `FileReader` 读取文本

### 2. PDF/Word文件
- 使用原有解析接口获取文本内容
- 或使用 `convertToPDF()` 转换后处理

### 3. 文件预览
- 使用 `getFilePath()` 在新窗口预览
- 支持多种文件格式的在线预览

### 4. 文件下载
- 使用 `download()` 获取文件数据
- 创建下载链接供用户下载

## ⚠️ 注意事项

### 1. 错误处理
- 所有异步操作都需要添加错误处理
- 提供备用方案（原有接口）
- 给用户明确的错误提示

### 2. 文件类型支持
- 不同文件类型需要不同的处理方式
- 纯文本文件可以直接读取
- 复杂格式文件需要服务端解析

### 3. 性能考虑
- 大文件下载可能耗时较长
- 需要显示加载状态
- 考虑文件大小限制

### 4. 安全性
- 验证文件类型和大小
- 防止恶意文件上传
- 确保文件访问权限

## 🚀 最佳实践

1. **优雅降级**: 优先使用UploadDoc方法，失败时使用原有接口
2. **用户反馈**: 提供清晰的加载状态和错误提示
3. **类型检测**: 根据文件扩展名选择合适的处理方式
4. **资源管理**: 及时释放blob URL等资源
5. **异常处理**: 完善的try-catch和Promise错误处理

通过这些方法，您可以充分利用UploadDoc组件的功能来处理各种类型的合同文件，为AI合同审查提供更强大的文件处理能力。

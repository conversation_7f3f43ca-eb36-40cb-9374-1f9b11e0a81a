<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>AI合同审查 - 真实数据格式测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://unpkg.com/showdown/dist/showdown.min.js"></script>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
        }
        
        .test-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .data-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
            margin: 10px 0;
            border-left: 4px solid #409eff;
            white-space: pre-wrap;
        }

        .ai-review-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div id="app" class="test-container">
        <div class="test-header">
            <h1>AI合同审查 - 真实数据格式测试</h1>
            <p>测试处理 mainData.textList 真实数据格式的功能</p>
        </div>

        <div class="data-section">
            <h3>📋 真实数据格式</h3>
            <p>mainData.textList 的实际数据结构：</p>
            <div class="code-block">{{ JSON.stringify(realDataFormat, null, 2) }}</div>
            
            <h4>解析后的文件列表：</h4>
            <el-table :data="parsedFiles" border style="width: 100%; margin-top: 10px;">
                <el-table-column prop="name" label="文件名" width="300"></el-table-column>
                <el-table-column prop="id" label="文档ID" width="280"></el-table-column>
                <el-table-column prop="status" label="状态" width="200"></el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-button size="mini" @click="selectFile(scope.row)">选择</el-button>
                    </template>
                </el-table-column>
            </el-table>
            
            <div style="margin-top: 20px;">
                <el-button @click="showAIReview = true" type="primary" size="medium">
                    打开AI合同审查（使用真实数据格式）
                </el-button>
            </div>
        </div>

        <!-- AI合同审查弹窗 -->
        <el-dialog
            title="AI合同审查 - 真实数据格式测试"
            :visible.sync="showAIReview"
            width="95%"
            top="2vh">
            <ai-contract-review
                v-if="showAIReview"
                :contract-files="realDataFormat"
                @close="showAIReview = false">
            </ai-contract-review>
        </el-dialog>
    </div>

    <script>
        // AI合同审查组件（简化版，专门处理真实数据格式）
        const AIContractReview = {
            props: {
                contractFiles: {
                    type: Array,
                    default: () => []
                }
            },
            data() {
                return {
                    form: { complianceText: "" },
                    activeParty: 'all',
                    response: { 
                        all: `## AI合同审查系统 - 真实数据格式测试
- **数据格式**: 处理真实的 mainData.textList 数据格式
- **文件结构**: {docId, name, status}

### 当前状态
正在处理真实数据格式的文件信息...`, 
                        partyA: `## 甲方立场审查
处理真实数据格式中...`, 
                        partyB: `## 乙方立场审查
处理真实数据格式中...`
                    },
                    isLoading: false,
                    currentFileInfo: null,
                    selectedFileIndex: 0
                }
            },
            computed: {
                complianceTextWithDefault() {
                    return this.form.complianceText || '等待文件解析...';
                },
                renderedMarkdown() {
                    const converter = new showdown.Converter();
                    return converter.makeHtml(this.response[this.activeParty]);
                },
                // 处理真实数据格式的可用文件列表
                availableFiles() {
                    const files = [];
                    if (this.contractFiles && this.contractFiles.length > 0) {
                        this.contractFiles.forEach((item, index) => {
                            if (item) {
                                // 处理新的数据格式：直接包含docId, name, status的对象
                                if (item.docId && item.name) {
                                    files.push({
                                        name: item.name,
                                        id: item.docId,
                                        status: item.status,
                                        originalIndex: index,
                                        data: item
                                    });
                                }
                            }
                        });
                    }
                    console.log('解析后的可用文件:', files);
                    return files;
                }
            },
            mounted() {
                console.log('AI合同审查组件已挂载');
                console.log('接收到的contractFiles:', this.contractFiles);
                console.log('解析后的availableFiles:', this.availableFiles);
                this.fetchComplianceText();
            },
            methods: {
                fetchComplianceText() {
                    this.isLoading = true;
                    
                    if (this.availableFiles.length === 0) {
                        this.$message.error('未找到合同文件');
                        this.isLoading = false;
                        return;
                    }
                    
                    const selectedFile = this.availableFiles[this.selectedFileIndex] || this.availableFiles[0];
                    const docId = selectedFile.id;
                    this.currentFileInfo = selectedFile.name;
                    
                    console.log('选中的文件:', selectedFile);
                    console.log('使用的docId:', docId);
                    
                    // 模拟接口调用
                    setTimeout(() => {
                        this.form.complianceText = `模拟解析结果 - 真实数据格式测试

文档ID: ${docId}
文件名: ${selectedFile.name}
状态: ${selectedFile.status}

这是模拟的合同文本内容：

甲方：测试公司A
乙方：测试公司B

合同主要条款：
1. 服务内容：技术开发服务
2. 合同金额：100万元人民币
3. 履行期限：12个月
4. 付款方式：分三期付款
5. 违约责任：按合同金额的10%承担违约金

特别条款：
- 知识产权归甲方所有
- 乙方需提供技术支持服务
- 项目验收标准按附件执行

风险提示：
- 需要明确技术交付标准
- 建议完善验收条款
- 注意付款节点的风险控制`;

                        this.isLoading = false;
                        this.$message.success('文本内容解析成功（模拟）');
                    }, 2000);
                },
                
                onFileChange(index) {
                    this.selectedFileIndex = index;
                    this.fetchComplianceText();
                },
                
                submitForm() {
                    this.isLoading = true;
                    
                    setTimeout(() => {
                        const mockResponse = `## ${this.activeParty === 'all' ? '全面' : this.activeParty === 'partyA' ? '甲方立场' : '乙方立场'}审查结果

### 真实数据格式处理成功 ✅

**当前分析文件:** ${this.currentFileInfo || '未知文件'}
**文档ID:** ${this.availableFiles[this.selectedFileIndex]?.id || 'N/A'}

### 合同分析结果

**主要风险点:**
1. 技术交付标准需要进一步细化
2. 验收条款存在模糊表述
3. 知识产权条款需要完善
4. 付款节点风险需要关注

**建议修改:**
1. 增加详细的技术规格说明书
2. 完善分阶段验收机制和标准
3. 明确知识产权归属和使用范围
4. 设置合理的付款保障机制

### 数据处理状态
- ✅ 真实数据格式解析成功
- ✅ docId字段提取成功
- ✅ 文件信息显示正常
- ✅ 模拟接口调用完成

### 技术细节
- 数据格式: {docId, name, status}
- 文件ID: ${this.availableFiles[this.selectedFileIndex]?.id}
- 文件名: ${this.availableFiles[this.selectedFileIndex]?.name}
- 状态: ${this.availableFiles[this.selectedFileIndex]?.status}`;

                        this.response[this.activeParty] = mockResponse;
                        this.isLoading = false;
                        
                        this.$message.success('AI审查完成！真实数据格式处理正常');
                    }, 2000);
                }
            },
            template: `
                <div style="padding: 20px; min-height: 600px;">
                    <div v-if="isLoading" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); display: flex; justify-content: center; align-items: center; z-index: 9999;">
                        <div style="background: white; padding: 30px; border-radius: 8px; text-align: center;">
                            <div style="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #409eff; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
                            <div>{{ selectedFileIndex >= 0 ? '正在解析文件...' : '正在分析合同...' }}</div>
                        </div>
                    </div>
                    
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-card>
                                <div slot="header">
                                    <span>📄 合同文本</span>
                                    <el-tag v-if="currentFileInfo" size="small" type="success" style="margin-left: 10px;">{{ currentFileInfo }}</el-tag>
                                    <el-tag size="small" type="info" style="margin-left: 5px;">{{ availableFiles.length }} 个文件</el-tag>
                                </div>
                                
                                <div v-if="availableFiles.length > 1" style="margin-bottom: 16px;">
                                    <el-select v-model="selectedFileIndex" @change="onFileChange" placeholder="选择要分析的合同文件" style="width: 100%;">
                                        <el-option v-for="(file, index) in availableFiles" :key="index" :label="file.name" :value="index">
                                            <span style="float: left">{{ file.name }}</span>
                                            <span style="float: right; color: #8492a6; font-size: 13px">{{ file.id.substring(0, 8) }}...</span>
                                        </el-option>
                                    </el-select>
                                </div>
                                
                                <div style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 15px; min-height: 300px; white-space: pre-line; font-family: monospace; font-size: 13px;">
                                    {{ complianceTextWithDefault }}
                                </div>
                                
                                <div style="text-align: center; margin-top: 15px;">
                                    <el-button type="primary" @click="submitForm" :loading="isLoading">
                                        {{ isLoading ? '分析中...' : '🤖 开始AI审查' }}
                                    </el-button>
                                </div>
                            </el-card>
                        </el-col>
                        
                        <el-col :span="12">
                            <el-card>
                                <div slot="header">
                                    <span>📊 审查结果</span>
                                    <el-radio-group v-model="activeParty" size="small" style="float: right;">
                                        <el-radio-button label="all">全部立场</el-radio-button>
                                        <el-radio-button label="partyA">甲方立场</el-radio-button>
                                        <el-radio-button label="partyB">乙方立场</el-radio-button>
                                    </el-radio-group>
                                </div>
                                
                                <div style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 15px; min-height: 300px; overflow-y: auto;" v-html="renderedMarkdown"></div>
                            </el-card>
                        </el-col>
                    </el-row>
                </div>
            `
        };

        // 主应用
        new Vue({
            el: '#app',
            components: {
                'ai-contract-review': AIContractReview
            },
            data: {
                showAIReview: false,
                // 真实的数据格式
                realDataFormat: [
                    {
                        "docId": "19799DBF1CBF47F79A523E19A05A4EB5",
                        "name": "sojrSalesContract_A1A3_WAC25070050.pdf",
                        "status": "sojrSalesContract_A1A3_WAC25070050.pdf"
                    },
                    {
                        "docId": "28899CBF2DBF58G89B634F29B16B5FC6",
                        "name": "技术开发协议_补充条款.docx",
                        "status": "技术开发协议_补充条款.docx"
                    },
                    {
                        "docId": "37799ABF3EBF69H99C745G39C27C6GD7",
                        "name": "保密协议_NDA_2024.pdf",
                        "status": "保密协议_NDA_2024.pdf"
                    }
                ]
            },
            computed: {
                parsedFiles() {
                    return this.realDataFormat.map(item => ({
                        name: item.name,
                        id: item.docId,
                        status: item.status
                    }));
                }
            },
            methods: {
                selectFile(file) {
                    this.$message.info(`选中文件: ${file.name}`);
                }
            }
        });
    </script>
    
    <style>
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</body>
</html>

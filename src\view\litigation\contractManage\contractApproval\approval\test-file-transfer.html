<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>AI合同审查 - 文件传递测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://unpkg.com/showdown/dist/showdown.min.js"></script>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
        }
        
        .test-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .mock-data-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .ai-review-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .file-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div id="app" class="test-container">
        <div class="test-header">
            <h1>AI合同审查 - 文件传递功能测试</h1>
            <p>测试合同文件数据传递到AI审查组件的功能</p>
        </div>

        <div class="mock-data-section">
            <h3>模拟合同文件数据</h3>
            <div class="file-info">
                <strong>文件1:</strong> {{ JSON.stringify(mockContractFiles[0], null, 2) }}
            </div>
            <div class="file-info" v-if="mockContractFiles[1]">
                <strong>文件2:</strong> {{ JSON.stringify(mockContractFiles[1], null, 2) }}
            </div>
            
            <el-button @click="showAIReview = true" type="primary" style="margin-top: 10px;">
                打开AI合同审查
            </el-button>
        </div>

        <!-- AI合同审查弹窗 -->
        <el-dialog
            title="AI合同审查 - 文件传递测试"
            :visible.sync="showAIReview"
            width="95%"
            top="2vh">
            <ai-contract-review
                v-if="showAIReview"
                :contract-files="mockContractFiles"
                @close="showAIReview = false">
            </ai-contract-review>
        </el-dialog>
    </div>

    <script>
        // AI合同审查组件
        const AIContractReview = {
            props: {
                contractFiles: {
                    type: Array,
                    default: () => []
                }
            },
            data() {
                return {
                    form: { complianceText: "" },
                    matches: [],
                    currentMatch: 0,
                    activeParty: 'all',
                    response: { 
                        all: `## AI合同审查系统
- **文件传递测试**: 验证合同文件数据传递功能
- **接口调用**: 使用传递的文件ID调用解析接口

### 当前状态
等待文件数据传递和接口调用...`, 
                        partyA: `## 甲方立场审查
等待文件数据传递...`, 
                        partyB: `## 乙方立场审查
等待文件数据传递...`
                    },
                    isLoading: false,
                    currentFileInfo: null,
                    selectedFileIndex: 0
                }
            },
            computed: {
                complianceTextWithDefault() {
                    return this.form.complianceText || '等待文件解析...';
                },
                renderedMarkdown() {
                    const converter = new showdown.Converter();
                    return converter.makeHtml(this.response[this.activeParty]);
                },
                availableFiles() {
                    const files = [];
                    if (this.contractFiles && this.contractFiles.length > 0) {
                        this.contractFiles.forEach((item, index) => {
                            if (item && item.attachment) {
                                try {
                                    const attachmentData = JSON.parse(item.attachment);
                                    if (attachmentData && attachmentData.length > 0) {
                                        attachmentData.forEach((file, fileIndex) => {
                                            files.push({
                                                name: file.fileName || file.name || `合同文件${index + 1}-${fileIndex + 1}`,
                                                id: file.id || file.fileId || file.fjid,
                                                originalIndex: index,
                                                fileIndex: fileIndex,
                                                data: file
                                            });
                                        });
                                    }
                                } catch (e) {
                                    console.warn('解析attachment数据失败:', e);
                                }
                            }
                        });
                    }
                    return files;
                }
            },
            mounted() {
                console.log('AI合同审查组件已挂载，接收到的文件数据:', this.contractFiles);
                this.fetchComplianceText();
            },
            methods: {
                fetchComplianceText() {
                    this.isLoading = true;
                    
                    let fileId = null;
                    
                    // 从传入的合同文件中提取文件ID
                    if (this.availableFiles.length > 0) {
                        const selectedFile = this.availableFiles[this.selectedFileIndex] || this.availableFiles[0];
                        fileId = selectedFile.id;
                        this.currentFileInfo = selectedFile.name;
                        
                        console.log('选中的文件:', selectedFile);
                        console.log('文件ID:', fileId);
                    }
                    
                    // 如果没有从合同文件中获取到ID，则从URL参数获取
                    if (!fileId) {
                        const urlParams = new URLSearchParams(window.location.search);
                        fileId = urlParams.get("fjid");
                    }
                    
                    // 模拟接口调用
                    setTimeout(() => {
                        if (fileId) {
                            this.form.complianceText = `模拟解析结果 - 文件ID: ${fileId}
                            
这是一个测试合同文本内容。

甲方：测试公司A
乙方：测试公司B

合同主要条款：
1. 服务内容：提供技术开发服务
2. 合同金额：100万元
3. 履行期限：12个月
4. 付款方式：分期付款
5. 违约责任：按合同金额的10%承担违约金

风险提示：
- 需要明确技术交付标准
- 建议完善验收条款
- 注意知识产权归属问题`;
                        } else {
                            this.form.complianceText = '未找到有效的文件ID，无法解析合同内容';
                        }
                        this.isLoading = false;
                    }, 2000);
                },
                
                onFileChange(index) {
                    this.selectedFileIndex = index;
                    this.fetchComplianceText();
                },
                
                submitForm() {
                    this.isLoading = true;
                    
                    setTimeout(() => {
                        const mockResponse = `## ${this.activeParty === 'all' ? '全面' : this.activeParty === 'partyA' ? '甲方立场' : '乙方立场'}审查结果

### 文件传递测试成功 ✅

**当前分析文件:** ${this.currentFileInfo || '未知文件'}

### 合同分析结果

**主要风险点:**
1. 技术交付标准不够明确
2. 验收条款需要完善
3. 知识产权归属需要明确

**建议修改:**
1. 增加详细的技术规格说明
2. 完善分阶段验收机制
3. 明确知识产权归属条款

### 接口调用状态
- ✅ 文件数据传递成功
- ✅ 文件ID提取成功
- ✅ 模拟接口调用完成`;

                        this.response[this.activeParty] = mockResponse;
                        this.isLoading = false;
                        
                        this.$message.success('AI审查完成！文件传递功能正常');
                    }, 2000);
                }
            },
            template: `
                <div style="padding: 20px; min-height: 600px;">
                    <div v-if="isLoading" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); display: flex; justify-content: center; align-items: center; z-index: 9999;">
                        <div style="background: white; padding: 30px; border-radius: 8px; text-align: center;">
                            <div style="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #409eff; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div>
                            <div>{{ selectedFileIndex >= 0 ? '正在解析文件...' : '正在分析合同...' }}</div>
                        </div>
                    </div>
                    
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-card>
                                <div slot="header">
                                    <span>📄 合同文本</span>
                                    <el-tag v-if="currentFileInfo" size="small" type="success" style="margin-left: 10px;">{{ currentFileInfo }}</el-tag>
                                </div>
                                
                                <div v-if="availableFiles.length > 1" style="margin-bottom: 16px;">
                                    <el-select v-model="selectedFileIndex" @change="onFileChange" placeholder="选择要分析的合同文件" style="width: 100%;">
                                        <el-option v-for="(file, index) in availableFiles" :key="index" :label="file.name" :value="index"></el-option>
                                    </el-select>
                                </div>
                                
                                <div style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 15px; min-height: 300px; white-space: pre-line; font-family: monospace; font-size: 13px;">
                                    {{ complianceTextWithDefault }}
                                </div>
                                
                                <div style="text-align: center; margin-top: 15px;">
                                    <el-button type="primary" @click="submitForm" :loading="isLoading">
                                        {{ isLoading ? '分析中...' : '🤖 开始AI审查' }}
                                    </el-button>
                                </div>
                            </el-card>
                        </el-col>
                        
                        <el-col :span="12">
                            <el-card>
                                <div slot="header">
                                    <span>📊 审查结果</span>
                                    <el-radio-group v-model="activeParty" size="small" style="float: right;">
                                        <el-radio-button label="all">全部立场</el-radio-button>
                                        <el-radio-button label="partyA">甲方立场</el-radio-button>
                                        <el-radio-button label="partyB">乙方立场</el-radio-button>
                                    </el-radio-group>
                                </div>
                                
                                <div style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 15px; min-height: 300px; overflow-y: auto;" v-html="renderedMarkdown"></div>
                            </el-card>
                        </el-col>
                    </el-row>
                </div>
            `
        };

        // 主应用
        new Vue({
            el: '#app',
            components: {
                'ai-contract-review': AIContractReview
            },
            data: {
                showAIReview: false,
                mockContractFiles: [
                    {
                        attachment: JSON.stringify([
                            {
                                id: "file001",
                                fjid: "file001",
                                fileName: "技术开发合同.pdf",
                                name: "技术开发合同.pdf",
                                fileSize: "2.5MB",
                                uploadTime: "2024-01-15"
                            },
                            {
                                id: "file002", 
                                fjid: "file002",
                                fileName: "补充协议.docx",
                                name: "补充协议.docx",
                                fileSize: "1.2MB",
                                uploadTime: "2024-01-16"
                            }
                        ])
                    },
                    {
                        attachment: JSON.stringify([
                            {
                                id: "file003",
                                fjid: "file003", 
                                fileName: "保密协议.pdf",
                                name: "保密协议.pdf",
                                fileSize: "800KB",
                                uploadTime: "2024-01-17"
                            }
                        ])
                    }
                ]
            }
        });
    </script>
    
    <style>
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>AI合同审查测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://unpkg.com/showdown/dist/showdown.min.js"></script>
    <style>
        .compliance-page { 
            padding: 20px; 
        }
        .box-card { 
            width: 100%; 
        }
        .markdown-content { 
            padding: 10px; 
            border: 1px solid #f9f9f9; 
            border-radius: 4px; 
            background-color: #f9f9f9; 
            min-height: 400px;
        }
        .markdown-container { 
            height: 400px; 
            overflow-y: auto; 
            border: 1px solid #ccc; 
            border-radius: 4px; 
            background-color: #f9f9f9; 
            padding: 10px; 
        }
        .markdown-preview { 
            margin-top: 12px; 
            padding: 10px; 
            border: 1px solid #ccc; 
            border-radius: 4px; 
            background-color: #f9f9f9; 
            overflow-y: auto; 
            height: 400px; 
            white-space: pre-line; 
        }
        .full-width-radio-group { 
            width: 100%; 
            display: flex; 
            justify-content: space-between; 
        }
        .full-width-radio-button { 
            flex: 1; 
            text-align: center; 
            margin: 0 !important; 
        }
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            z-index: 9999;
        }
        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #409eff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }
        .loading-text {
            margin-top: 10px;
            font-size: 16px;
            color: #409eff;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div id="app" class="compliance-page">
        <!-- 加载遮罩 -->
        <div v-if="isLoading" class="loading-overlay">
            <div class="loading-spinner"></div>
            <div class="loading-text">加载中...</div>
        </div>
        
        <h2>AI合同审查测试页面</h2>
        
        <el-row :gutter="20">
            <!-- 左侧：合同文本显示区域 -->
            <el-col :span="12">
                <el-card>
                    <div slot="header">
                        <span>合同文本</span>
                    </div>
                    <div>
                        <div class="markdown-preview">{{ complianceTextWithDefault }}</div>
                        <el-row style="margin-top: 10px;">
                            <el-col :span="24" style="text-align: center;">
                                <el-button type="success" @click="submitForm">提交审查</el-button>
                            </el-col>
                        </el-row>
                    </div>
                </el-card>
            </el-col>
            
            <!-- 右侧：审查结果显示区域 -->
            <el-col :span="12">
                <el-card>
                    <div slot="header">
                        <el-radio-group v-model="activeParty" class="full-width-radio-group">
                            <el-radio-button label="all" class="full-width-radio-button">全部立场</el-radio-button>
                            <el-radio-button label="partyA" class="full-width-radio-button">甲方立场</el-radio-button>
                            <el-radio-button label="partyB" class="full-width-radio-button">乙方立场</el-radio-button>
                        </el-radio-group>
                    </div>
                    <div class="markdown-container">
                        <div class="markdown-content" v-html="renderedMarkdown"></div>
                    </div>
                </el-card>
            </el-col>
        </el-row>
    </div>

    <script>
        new Vue({
            el: '#app',
            data: {
                form: { 
                    complianceText: "这是一个测试合同文本。\n\n甲方：测试公司A\n乙方：测试公司B\n\n合同内容：\n1. 甲方向乙方提供服务\n2. 乙方向甲方支付费用\n3. 合同期限为一年" 
                },
                activeParty: 'all',
                response: { 
                    all: `## AI合同审查系统
- **点击提交**: 获取审查内容
- **划词匹配**: 支持文本高亮和匹配功能

### 示例操作步骤

1. 点击"提交审查"按钮以获取审查内容。
2. 切换不同立场查看不同的审查结果。`, 
                    partyA: `## 甲方立场审查
- **点击提交**: 获取审查内容
- **划词匹配**: 支持文本高亮和匹配功能

### 示例操作步骤

1. 点击"提交审查"按钮以获取审查内容。
2. 从甲方角度分析合同条款。`, 
                    partyB: `## 乙方立场审查
- **点击提交**: 获取审查内容
- **划词匹配**: 支持文本高亮和匹配功能

### 示例操作步骤

1. 点击"提交审查"按钮以获取审查内容。
2. 从乙方角度分析合同条款。`, 
                },
                isLoading: false
            },
            computed: {
                complianceTextWithDefault() {
                    return this.form.complianceText || '解析文本中......';
                },
                renderedMarkdown() {
                    const converter = new showdown.Converter();
                    return converter.makeHtml(this.response[this.activeParty]);
                }
            },
            methods: {
                submitForm() {
                    // 模拟提交过程
                    this.isLoading = true;
                    
                    // 模拟API调用
                    setTimeout(() => {
                        const mockResponse = `## ${this.activeParty === 'all' ? '全面' : this.activeParty === 'partyA' ? '甲方立场' : '乙方立场'}审查结果

### 合同分析

**合同主体：**
- 甲方：测试公司A
- 乙方：测试公司B

**主要条款分析：**

1. **服务条款**
   - 甲方提供服务的具体内容需要进一步明确
   - 建议详细描述服务标准和质量要求

2. **付款条款**
   - 乙方付款方式和时间需要明确约定
   - 建议增加逾期付款的违约责任条款

3. **合同期限**
   - 一年期限较为合理
   - 建议增加续约条款

### 风险提示

${this.activeParty === 'partyA' ? 
'- 作为甲方，需要注意服务质量保证\n- 建议增加服务验收标准' : 
this.activeParty === 'partyB' ? 
'- 作为乙方，需要注意付款义务\n- 建议争取更灵活的付款条件' : 
'- 双方权利义务需要平衡\n- 建议完善争议解决机制'}

### 建议修改

1. 完善服务描述条款
2. 明确付款时间和方式
3. 增加违约责任条款
4. 完善争议解决机制`;

                        this.response[this.activeParty] = mockResponse;
                        this.isLoading = false;
                        
                        this.$message.success('审查完成！');
                    }, 2000);
                }
            }
        });
    </script>
</body>
</html>

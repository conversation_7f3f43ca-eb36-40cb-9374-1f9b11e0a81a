<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>AI合同审查测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://unpkg.com/showdown/dist/showdown.min.js"></script>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .ai-contract-review {
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .page-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            color: white;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
        }

        .header-title {
            margin: 0 0 8px 0;
            font-size: 28px;
            font-weight: 600;
        }

        .header-subtitle {
            margin: 0;
            font-size: 16px;
            opacity: 0.9;
        }

        .text-card, .result-card {
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            background: white;
        }

        .text-card:hover, .result-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .card-header {
            padding: 20px 24px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 1px solid #e9ecef;
            font-weight: 600;
            color: #2c3e50;
        }

        .markdown-preview {
            background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.6;
            color: #2c3e50;
            white-space: pre-line;
            overflow-y: auto;
            height: 400px;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
        }

        .markdown-container {
            background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            height: 400px;
            overflow-y: auto;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
        }

        .submit-btn {
            padding: 12px 32px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            backdrop-filter: blur(10px);
        }

        .loading-content {
            text-align: center;
            color: white;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        .loading-text {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .loading-subtitle {
            font-size: 14px;
            opacity: 0.8;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .el-card {
            border: none;
        }

        .el-card__body {
            padding: 24px;
        }
    </style>
</head>
<body>
    <div id="app" class="ai-contract-review">
        <!-- 美化的加载遮罩 -->
        <div v-if="isLoading" class="loading-overlay">
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <div class="loading-text">AI正在分析合同内容...</div>
                <div class="loading-subtitle">请稍候，这可能需要几秒钟</div>
            </div>
        </div>

        <!-- 页面标题 -->
        <div class="page-header">
            <h2 class="header-title">AI智能合同审查</h2>
            <p class="header-subtitle">基于人工智能的专业合同风险分析与建议</p>
        </div>

        <el-row :gutter="24">
            <!-- 左侧：合同文本显示区域 -->
            <el-col :span="12">
                <div class="text-card">
                    <div class="card-header">
                        <span>📄 合同文本</span>
                        <el-tag size="small" type="info">{{ form.complianceText ? form.complianceText.length : 0 }} 字符</el-tag>
                    </div>
                    <div class="el-card__body">
                        <div class="markdown-preview">{{ complianceTextWithDefault }}</div>
                        <div style="text-align: center;">
                            <button class="submit-btn" @click="submitForm" :disabled="isLoading">
                                {{ isLoading ? '分析中...' : '🤖 开始AI审查' }}
                            </button>
                        </div>
                    </div>
                </div>
            </el-col>

            <!-- 右侧：审查结果显示区域 -->
            <el-col :span="12">
                <div class="result-card">
                    <div class="card-header">
                        <span>📊 审查结果</span>
                        <el-radio-group v-model="activeParty" size="small">
                            <el-radio-button label="all">全部立场</el-radio-button>
                            <el-radio-button label="partyA">甲方立场</el-radio-button>
                            <el-radio-button label="partyB">乙方立场</el-radio-button>
                        </el-radio-group>
                    </div>
                    <div class="el-card__body">
                        <div class="markdown-container" v-html="renderedMarkdown"></div>
                    </div>
                </div>
            </el-col>
        </el-row>
    </div>

    <script>
        new Vue({
            el: '#app',
            data: {
                form: { 
                    complianceText: "这是一个测试合同文本。\n\n甲方：测试公司A\n乙方：测试公司B\n\n合同内容：\n1. 甲方向乙方提供服务\n2. 乙方向甲方支付费用\n3. 合同期限为一年" 
                },
                activeParty: 'all',
                response: { 
                    all: `## AI合同审查系统
- **点击提交**: 获取审查内容
- **划词匹配**: 支持文本高亮和匹配功能

### 示例操作步骤

1. 点击"提交审查"按钮以获取审查内容。
2. 切换不同立场查看不同的审查结果。`, 
                    partyA: `## 甲方立场审查
- **点击提交**: 获取审查内容
- **划词匹配**: 支持文本高亮和匹配功能

### 示例操作步骤

1. 点击"提交审查"按钮以获取审查内容。
2. 从甲方角度分析合同条款。`, 
                    partyB: `## 乙方立场审查
- **点击提交**: 获取审查内容
- **划词匹配**: 支持文本高亮和匹配功能

### 示例操作步骤

1. 点击"提交审查"按钮以获取审查内容。
2. 从乙方角度分析合同条款。`, 
                },
                isLoading: false
            },
            computed: {
                complianceTextWithDefault() {
                    return this.form.complianceText || '解析文本中......';
                },
                renderedMarkdown() {
                    const converter = new showdown.Converter();
                    return converter.makeHtml(this.response[this.activeParty]);
                }
            },
            methods: {
                submitForm() {
                    // 模拟提交过程
                    this.isLoading = true;
                    
                    // 模拟API调用
                    setTimeout(() => {
                        const mockResponse = `## ${this.activeParty === 'all' ? '全面' : this.activeParty === 'partyA' ? '甲方立场' : '乙方立场'}审查结果

### 合同分析

**合同主体：**
- 甲方：测试公司A
- 乙方：测试公司B

**主要条款分析：**

1. **服务条款**
   - 甲方提供服务的具体内容需要进一步明确
   - 建议详细描述服务标准和质量要求

2. **付款条款**
   - 乙方付款方式和时间需要明确约定
   - 建议增加逾期付款的违约责任条款

3. **合同期限**
   - 一年期限较为合理
   - 建议增加续约条款

### 风险提示

${this.activeParty === 'partyA' ? 
'- 作为甲方，需要注意服务质量保证\n- 建议增加服务验收标准' : 
this.activeParty === 'partyB' ? 
'- 作为乙方，需要注意付款义务\n- 建议争取更灵活的付款条件' : 
'- 双方权利义务需要平衡\n- 建议完善争议解决机制'}

### 建议修改

1. 完善服务描述条款
2. 明确付款时间和方式
3. 增加违约责任条款
4. 完善争议解决机制`;

                        this.response[this.activeParty] = mockResponse;
                        this.isLoading = false;
                        
                        this.$message.success('审查完成！');
                    }, 2000);
                }
            }
        });
    </script>
</body>
</html>

# AI合同审查文件传递功能实现

## 🎯 功能概述

实现了将合同文本附件从主页面传递到AI审查弹窗页面，并通过原有接口将文件ID传输给第一个解析接口的完整功能。

## ✨ 主要实现内容

### 1. 数据传递机制

#### 📤 主页面数据传递
```vue
<!-- ContractApprovalMain.vue -->
<AIContractReview
  v-if="aiReviewDialogVisible"
  :contract-files="mainData.textList"
  @close="handleAIReviewClose"/>
```

#### 📥 子组件数据接收
```vue
<!-- AIContractReview.vue -->
props: {
  contractFiles: {
    type: Array,
    default: () => []
  }
}
```

### 2. 文件数据解析

#### 🔍 文件列表解析
```javascript
// 计算属性：解析所有可用文件
availableFiles() {
  const files = [];
  if (this.contractFiles && this.contractFiles.length > 0) {
    this.contractFiles.forEach((item, index) => {
      if (item && item.attachment) {
        try {
          const attachmentData = JSON.parse(item.attachment);
          if (attachmentData && attachmentData.length > 0) {
            attachmentData.forEach((file, fileIndex) => {
              files.push({
                name: file.fileName || file.name || `合同文件${index + 1}-${fileIndex + 1}`,
                id: file.id || file.fileId || file.fjid,
                originalIndex: index,
                fileIndex: fileIndex,
                data: file
              });
            });
          }
        } catch (e) {
          console.warn('解析attachment数据失败:', e);
        }
      }
    });
  }
  return files;
}
```

### 3. 文件选择功能

#### 📋 文件选择器UI
```vue
<div v-if="availableFiles.length > 1" class="file-selector">
  <el-select 
    v-model="selectedFileIndex" 
    @change="onFileChange"
    placeholder="选择要分析的合同文件"
    style="width: 100%; margin-bottom: 16px;">
    <el-option
      v-for="(file, index) in availableFiles"
      :key="index"
      :label="file.name"
      :value="index">
    </el-option>
  </el-select>
</div>
```

#### 🔄 文件切换处理
```javascript
onFileChange(index) {
  this.selectedFileIndex = index;
  // 重新获取文本内容
  this.fetchComplianceText();
}
```

### 4. 接口调用优化

#### 🔗 文件ID提取与传递
```javascript
fetchComplianceText() {
  this.isLoading = true;
  
  let fileId = null;
  
  // 从传入的合同文件中提取文件ID
  if (this.availableFiles.length > 0) {
    const selectedFile = this.availableFiles[this.selectedFileIndex] || this.availableFiles[0];
    fileId = selectedFile.id;
    this.currentFileInfo = selectedFile.name;
  }
  
  // 如果没有从合同文件中获取到ID，则从URL参数获取
  if (!fileId) {
    const urlParams = new URLSearchParams(window.location.search);
    fileId = urlParams.get("fjid");
  }
  
  // 如果仍然没有文件ID，显示错误
  if (!fileId) {
    this.$message.error('未找到合同文件ID，无法获取文本内容');
    this.isLoading = false;
    return;
  }
  
  // 调用原有接口
  const raw = JSON.stringify({
    "fjid": fileId
  });
  
  fetch("http://*************:5004/mobilemode/api/htsc/htsc02/api_hgsc", requestOptions)
    .then(response => response.json())
    .then(result => {
      const wenbeng = result.wenben;
      this.form.complianceText = wenbeng;
      this.isLoading = false;
    })
    .catch(error => {
      console.error('error', error);
      this.$message.error('获取文本失败，请检查网络或接口');
      this.isLoading = false;
    });
}
```

## 🎨 用户界面增强

### 1. 文件信息显示
- 在卡片头部显示当前选中的文件名
- 使用绿色标签突出显示文件信息
- 显示文本字符数统计

### 2. 文件选择器
- 当有多个文件时显示下拉选择器
- 美化的选择器样式
- 文件切换时自动重新解析

### 3. 状态反馈
- 加载状态提示
- 错误信息显示
- 成功状态反馈

## 🔧 技术实现细节

### 数据流程
```
主页面(mainData.textList) 
    ↓ props传递
AI审查组件(contractFiles) 
    ↓ 解析处理
可用文件列表(availableFiles) 
    ↓ 选择文件
提取文件ID(fileId) 
    ↓ 接口调用
获取文本内容(complianceText)
```

### 错误处理
1. **数据解析错误**: 捕获JSON解析异常
2. **文件ID缺失**: 提示用户并阻止无效请求
3. **接口调用失败**: 显示错误信息并恢复状态

### 兼容性处理
1. **向下兼容**: 支持URL参数方式获取文件ID
2. **多字段支持**: 支持id、fileId、fjid等多种字段名
3. **默认值处理**: 提供合理的默认文件名

## 📊 功能特性

### ✅ 已实现功能
- [x] 合同文件数据传递
- [x] 文件ID自动提取
- [x] 多文件选择支持
- [x] 原有接口调用
- [x] 文件信息显示
- [x] 错误处理机制
- [x] 加载状态管理

### 🎯 用户体验
- **智能识别**: 自动识别和解析文件数据
- **灵活选择**: 支持多文件切换选择
- **状态清晰**: 明确的加载和错误状态
- **信息透明**: 显示当前处理的文件信息

### 🔒 稳定性保证
- **异常捕获**: 完善的错误处理机制
- **数据验证**: 文件ID有效性检查
- **降级处理**: URL参数备用方案
- **用户提示**: 清晰的错误和状态信息

## 🧪 测试验证

### 测试页面功能
- 模拟真实的合同文件数据结构
- 验证文件传递和解析功能
- 测试多文件选择和切换
- 验证接口调用流程

### 测试用例覆盖
1. **单文件传递**: 验证基本文件传递功能
2. **多文件选择**: 测试文件选择器和切换功能
3. **数据解析**: 验证attachment数据解析
4. **接口调用**: 测试文件ID提取和接口调用
5. **错误处理**: 验证各种异常情况的处理

## 🚀 部署说明

### 依赖要求
- Vue.js 2.x
- Element UI
- Showdown (已安装)

### 配置要求
- 确保接口地址可访问
- 合同文件数据格式正确
- 文件ID字段命名一致

### 使用方法
1. 在合同审批页面点击"AI合同审查"按钮
2. 系统自动传递合同文件数据到弹窗
3. 如有多个文件，可通过下拉框选择
4. 系统自动调用解析接口获取文本内容
5. 点击"开始AI审查"进行智能分析

这个实现完全保持了原有接口的调用方式，同时增强了文件数据的传递和处理能力，为用户提供了更加便捷和智能的合同审查体验。

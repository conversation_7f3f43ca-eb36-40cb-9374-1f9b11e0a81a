<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>UploadDoc组件方法测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
        }
        
        .test-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .method-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
            margin: 10px 0;
            border-left: 4px solid #409eff;
        }

        .result-area {
            background: #f0f9ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #bfdbfe;
            min-height: 100px;
        }

        .method-card {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            margin: 10px 0;
        }

        .method-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .method-description {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 12px;
        }
    </style>
</head>
<body>
    <div id="app" class="test-container">
        <div class="test-header">
            <h1>UploadDoc组件方法测试</h1>
            <p>演示如何在AI合同审查中使用UploadDoc组件的各种方法</p>
        </div>

        <!-- 模拟文件数据 -->
        <div class="method-section">
            <h3>📁 模拟文件数据</h3>
            <el-select v-model="selectedFileId" placeholder="选择测试文件" style="width: 300px;">
                <el-option
                    v-for="file in mockFiles"
                    :key="file.id"
                    :label="file.name"
                    :value="file.id">
                </el-option>
            </el-select>
            <div class="code-block" v-if="selectedFile">
                <strong>当前选中文件:</strong><br>
                ID: {{ selectedFile.id }}<br>
                名称: {{ selectedFile.name }}<br>
                类型: {{ selectedFile.type }}<br>
                大小: {{ selectedFile.size }}
            </div>
        </div>

        <!-- 文件下载方法 -->
        <div class="method-section">
            <div class="method-card">
                <div class="method-title">📥 docApi.download(docId)</div>
                <div class="method-description">下载文件并获取blob数据，可用于文本提取</div>
                <el-button @click="testDownload" :disabled="!selectedFileId" type="primary">
                    测试下载方法
                </el-button>
                <div class="code-block">
docApi.download(docId).then(response => {
  const blob = response.data;
  // 处理blob数据
});
                </div>
                <div class="result-area">
                    <strong>执行结果:</strong><br>
                    {{ downloadResult }}
                </div>
            </div>
        </div>

        <!-- 文件预览方法 -->
        <div class="method-section">
            <div class="method-card">
                <div class="method-title">👁️ docApi.getFilePath(docId, orgId)</div>
                <div class="method-description">获取文件预览路径，在新窗口中打开</div>
                <el-button @click="testPreview" :disabled="!selectedFileId" type="success">
                    测试预览方法
                </el-button>
                <div class="code-block">
const previewPath = docApi.getFilePath(docId, orgId);
window.open(previewPath, '_blank');
                </div>
                <div class="result-area">
                    <strong>执行结果:</strong><br>
                    {{ previewResult }}
                </div>
            </div>
        </div>

        <!-- PDF转换方法 -->
        <div class="method-section">
            <div class="method-card">
                <div class="method-title">📄 docApi.convertToPDF(docId)</div>
                <div class="method-description">将文件转换为PDF格式</div>
                <el-button @click="testConvertPDF" :disabled="!selectedFileId" type="warning">
                    测试PDF转换
                </el-button>
                <div class="code-block">
docApi.convertToPDF(docId).then(response => {
  console.log('PDF转换成功:', response);
});
                </div>
                <div class="result-area">
                    <strong>执行结果:</strong><br>
                    {{ pdfResult }}
                </div>
            </div>
        </div>

        <!-- 文本提取方法 -->
        <div class="method-section">
            <div class="method-card">
                <div class="method-title">📝 文本提取综合方法</div>
                <div class="method-description">结合多种方法提取文件文本内容</div>
                <el-button @click="testTextExtraction" :disabled="!selectedFileId" type="info">
                    测试文本提取
                </el-button>
                <div class="code-block">
// 1. 下载文件
docApi.download(docId).then(response => {
  const blob = response.data;
  // 2. 根据文件类型处理
  if (fileName.endsWith('.txt')) {
    // 直接读取文本文件
    const reader = new FileReader();
    reader.onload = (e) => {
      const text = e.target.result;
      // 处理文本内容
    };
    reader.readAsText(blob, 'UTF-8');
  } else {
    // 使用原有接口解析
    fetchFromOriginalAPI(docId);
  }
});
                </div>
                <div class="result-area">
                    <strong>提取的文本内容:</strong><br>
                    <div style="max-height: 200px; overflow-y: auto; white-space: pre-line; font-family: monospace; font-size: 12px;">
                        {{ extractedText }}
                    </div>
                </div>
            </div>
        </div>

        <!-- 综合演示 -->
        <div class="method-section">
            <h3>🎯 AI合同审查集成演示</h3>
            <p>演示如何在AI合同审查中集成这些方法</p>
            <el-button @click="showIntegratedDemo = true" type="primary" size="medium">
                打开集成演示
            </el-button>
        </div>

        <!-- 集成演示弹窗 -->
        <el-dialog
            title="AI合同审查 - UploadDoc方法集成"
            :visible.sync="showIntegratedDemo"
            width="90%"
            top="5vh">
            <integrated-demo
                v-if="showIntegratedDemo"
                :selected-file="selectedFile"
                @close="showIntegratedDemo = false">
            </integrated-demo>
        </el-dialog>
    </div>

    <script>
        // 模拟docApi
        const mockDocApi = {
            download: (docId) => {
                return new Promise((resolve, reject) => {
                    setTimeout(() => {
                        if (docId === 'error-file') {
                            reject(new Error('下载失败'));
                        } else {
                            // 模拟不同类型的文件内容
                            let content = '';
                            if (docId === 'txt-file') {
                                content = '这是一个测试文本文件的内容。\n\n合同条款：\n1. 甲方义务\n2. 乙方义务\n3. 违约责任';
                            } else {
                                content = '模拟的二进制文件内容';
                            }
                            const blob = new Blob([content], { type: 'text/plain' });
                            resolve({ data: blob });
                        }
                    }, 1000);
                });
            },
            
            getFilePath: (docId, orgId) => {
                return `http://example.com/preview/${docId}?orgId=${orgId}`;
            },
            
            convertToPDF: (docId) => {
                return new Promise((resolve, reject) => {
                    setTimeout(() => {
                        if (docId === 'error-file') {
                            reject(new Error('PDF转换失败'));
                        } else {
                            resolve({ pdfId: `pdf_${docId}`, status: 'success' });
                        }
                    }, 2000);
                });
            }
        };

        // 集成演示组件
        const IntegratedDemo = {
            props: ['selectedFile'],
            data() {
                return {
                    isLoading: false,
                    extractedText: '',
                    currentStep: '',
                    logs: []
                };
            },
            methods: {
                addLog(message) {
                    this.logs.push(`[${new Date().toLocaleTimeString()}] ${message}`);
                },
                
                async demonstrateIntegration() {
                    if (!this.selectedFile) {
                        this.$message.warning('请先选择一个文件');
                        return;
                    }
                    
                    this.isLoading = true;
                    this.logs = [];
                    this.extractedText = '';
                    
                    try {
                        this.currentStep = '正在下载文件...';
                        this.addLog('开始下载文件: ' + this.selectedFile.name);
                        
                        const response = await mockDocApi.download(this.selectedFile.id);
                        this.addLog('文件下载成功');
                        
                        this.currentStep = '正在提取文本...';
                        
                        if (this.selectedFile.type === 'text/plain') {
                            // 处理文本文件
                            const reader = new FileReader();
                            reader.onload = (e) => {
                                this.extractedText = e.target.result;
                                this.addLog('文本提取成功，共 ' + this.extractedText.length + ' 字符');
                                this.currentStep = '文本提取完成';
                                this.isLoading = false;
                            };
                            reader.readAsText(response.data, 'UTF-8');
                        } else {
                            // 模拟使用原有接口
                            this.addLog('文件类型为 ' + this.selectedFile.type + '，使用原有接口解析');
                            setTimeout(() => {
                                this.extractedText = '模拟通过原有接口解析的合同文本内容：\n\n甲方：测试公司A\n乙方：测试公司B\n\n合同条款：\n1. 服务内容描述\n2. 付款条件\n3. 履行期限\n4. 违约责任';
                                this.addLog('原有接口解析完成');
                                this.currentStep = '文本解析完成';
                                this.isLoading = false;
                            }, 1500);
                        }
                        
                    } catch (error) {
                        this.addLog('错误: ' + error.message);
                        this.currentStep = '处理失败';
                        this.isLoading = false;
                    }
                }
            },
            template: `
                <div style="padding: 20px;">
                    <div style="margin-bottom: 20px;">
                        <h4>选中文件: {{ selectedFile ? selectedFile.name : '无' }}</h4>
                        <el-button @click="demonstrateIntegration" :loading="isLoading" type="primary">
                            {{ isLoading ? currentStep : '开始演示集成流程' }}
                        </el-button>
                    </div>
                    
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-card header="执行日志">
                                <div style="height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px;">
                                    <div v-for="log in logs" :key="log" style="margin: 2px 0;">{{ log }}</div>
                                </div>
                            </el-card>
                        </el-col>
                        <el-col :span="12">
                            <el-card header="提取的文本内容">
                                <div style="height: 300px; overflow-y: auto; white-space: pre-line; font-family: monospace; font-size: 12px; background: #f8f9fa; padding: 10px; border-radius: 4px;">
                                    {{ extractedText || '等待文本提取...' }}
                                </div>
                            </el-card>
                        </el-col>
                    </el-row>
                </div>
            `
        };

        // 主应用
        new Vue({
            el: '#app',
            components: {
                'integrated-demo': IntegratedDemo
            },
            data: {
                selectedFileId: '',
                downloadResult: '等待执行...',
                previewResult: '等待执行...',
                pdfResult: '等待执行...',
                extractedText: '等待执行...',
                showIntegratedDemo: false,
                mockFiles: [
                    { id: 'txt-file', name: '合同文本.txt', type: 'text/plain', size: '2KB' },
                    { id: 'pdf-file', name: '技术协议.pdf', type: 'application/pdf', size: '1.5MB' },
                    { id: 'doc-file', name: '服务合同.docx', type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', size: '800KB' },
                    { id: 'error-file', name: '错误文件.txt', type: 'text/plain', size: '1KB' }
                ]
            },
            computed: {
                selectedFile() {
                    return this.mockFiles.find(f => f.id === this.selectedFileId);
                }
            },
            methods: {
                testDownload() {
                    this.downloadResult = '正在下载...';
                    mockDocApi.download(this.selectedFileId)
                        .then(response => {
                            this.downloadResult = `下载成功！获取到blob数据，大小: ${response.data.size} 字节`;
                        })
                        .catch(error => {
                            this.downloadResult = `下载失败: ${error.message}`;
                        });
                },
                
                testPreview() {
                    const orgId = 'test-org-123';
                    const previewPath = mockDocApi.getFilePath(this.selectedFileId, orgId);
                    this.previewResult = `预览路径生成成功: ${previewPath}`;
                    // 实际使用中会打开新窗口
                    // window.open(previewPath, '_blank');
                },
                
                testConvertPDF() {
                    this.pdfResult = '正在转换PDF...';
                    mockDocApi.convertToPDF(this.selectedFileId)
                        .then(response => {
                            this.pdfResult = `PDF转换成功！PDF ID: ${response.pdfId}`;
                        })
                        .catch(error => {
                            this.pdfResult = `PDF转换失败: ${error.message}`;
                        });
                },
                
                testTextExtraction() {
                    this.extractedText = '正在提取文本...';
                    
                    mockDocApi.download(this.selectedFileId)
                        .then(response => {
                            const blob = response.data;
                            const fileName = this.selectedFile.name;
                            
                            if (fileName.endsWith('.txt')) {
                                // 读取文本文件
                                const reader = new FileReader();
                                reader.onload = (e) => {
                                    this.extractedText = e.target.result;
                                };
                                reader.onerror = () => {
                                    this.extractedText = '读取文件失败';
                                };
                                reader.readAsText(blob, 'UTF-8');
                            } else {
                                // 模拟使用原有接口
                                setTimeout(() => {
                                    this.extractedText = `通过原有接口解析的 ${fileName} 内容：\n\n这是模拟的合同文本内容...\n\n甲方：XXX公司\n乙方：YYY公司\n合同金额：100万元\n签订日期：2024年1月1日`;
                                }, 1000);
                            }
                        })
                        .catch(error => {
                            this.extractedText = `文本提取失败: ${error.message}`;
                        });
                }
            }
        });
    </script>
</body>
</html>

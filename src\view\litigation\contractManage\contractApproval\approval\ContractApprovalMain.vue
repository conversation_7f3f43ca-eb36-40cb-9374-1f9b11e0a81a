<template>
  <FormWindow>
    <el-container style="height: calc(100vh - 160px);">
      <!--流程查看页面-->
      <el-main>
        <el-card style="height: auto;margin-left: 1%;margin-right: 2%;margin-top: 1%;">
          <el-scrollbar id="pdfCentent" style="height: 100%;">
            <el-form id="print" ref="dataForm"
                     :class="className"
                     :model="mainData" label-width="120px" style="margin-left: 10px;margin-right: 10px;">
              <!-- 岗位职责显示行 -->
              <el-row v-if="currentNodeInfo" style="margin-top: 10px;">
                <el-col :span="24" style="text-align: center;">
                  <el-tag type="info" size="medium" style="padding: 8px 16px; font-size: 14px;">
                    <i class="el-icon-user-solid" style="margin-right: 5px;"></i>
                    当前岗位职责：{{ currentNodeInfo }}
                  </el-tag>
                </el-col>
              </el-row>

              <el-row style="margin-top: 20px;">
                <span style="text-align: left;font-size: 23px;margin-left: 43%;font-weight: 900;">新增合同审批单</span>
              </el-row>
              <!--通用合同-->
              <SimpleBoardTitleApproval style="margin-top: 5px" title="基本信息">
                <table class="table_content">
                  <tbody>
                  <tr>
                    <th class="th_label_approval" colspan="3">合同名称</th>
                    <td class="td_value_approval" colspan="9">{{ mainData.contractName }}</td>
                    <th class="th_label_approval" colspan="3">审批单号</th>
                    <td class="td_value_approval" colspan="9">{{ mainData.approvalCode }}</td>
                  </tr>
                  <tr>
                    <th class="th_label_approval" colspan="3">经办人</th>
                    <td class="td_value_approval" colspan="9">{{ mainData.createPsnFullName }}</td>
                    <th class="th_label_approval" colspan="3">经办时间</th>
                    <td class="td_value_approval" colspan="9">{{ mainData.createTime | date }}</td>
                  </tr>
                  <tr>
                    <th class="th_label_approval" colspan="3">经办人电话</th>
                    <td class="td_value_approval" colspan="9">{{ mainData.createPsnPhone }}</td>
                    <th class="th_label_approval" colspan="3">合同类型</th>
                    <td class="td_value_approval" colspan="9">{{ mainData.contractType }}</td>
                  </tr>
                  <tr>
                    <th class="th_label_approval" colspan="3">合同金额（元）</th>
                    <td class="td_value_approval" colspan="9">{{ mainData.contractMoney }}</td>
                    <th class="th_label_approval" colspan="3">项目名称</th>
                    <td class="td_value_approval" colspan="9">
                      {{ mainData.projectNames === null ? '无' : mainData.projectNames }}
                    </td>
                  </tr>
                  <tr>
                    <th class="th_label_approval" colspan="3">我方当事人</th>
                    <td class="td_value_approval" colspan="21">{{ mainData.ourPartyName }}</td>
                  </tr>
                  <tr>
                    <th class="th_label_approval" colspan="3">对方当事人</th>
                    <td class="td_value_approval" colspan="21">{{ mainData.otherPartyName }}</td>
                  </tr>
                  <tr>
                    <th class="th_label_approval" colspan="3">摘要说明</th>
                    <td class="td_value_approval" colspan="21">{{ mainData.summaryNote }}</td>
                  </tr>
                  <tr>
                    <th class="th_label_approval" colspan="3">合同文本</th>
                    <td class="td_value_approval" colspan="21" style="height: 100%">
                      <div style="margin-bottom: 10px;">
                        <el-button type="primary" size="small" @click="openAIContractReview">
                          AI合同审查
                        </el-button>
                      </div>
                      <div v-if="mainData.textList">
                        <div v-for="item in mainData.textList">
                          <uploadDoc v-model="item.attachment" :disabled="true" :doc-path="docURL"
                                     :show-pdf="true"
                                     :files.sync="item.attachment" :showEdit="editIsShow()"/>
                        </div>
                      </div>
                      <div v-else style="font-size: 15px">无</div>
                    </td>
                  </tr>
                  <tr>
                    <th class="th_label_approval" colspan="3">其他附件</th>
                    <td class="td_value_approval" colspan="21" style="height: 100%">
                      <div v-if="mainData.contractFiles">
                        <uploadDoc
                            v-model="mainData.contractFiles"
                            :doc-path="docURL"
                            :files.sync="mainData.contractFiles"
                            :tips="'根据相关管理要求，报告正文请线下加盖印章'"
                            disabled/>
                      </div>
                      <div v-else style="font-size: 15px">无</div>
                    </td>
                  </tr>
                  <tr>
                    <th class="th_label_approval" colspan="3">授权书</th>
                    <td class="td_value_approval" colspan="21" style="height: 100%">
                      <div v-if="mainData.authorization && mainData.authorization.authTakeEffectFile">
                        <uploadDoc
                            v-model="mainData.authorization.authTakeEffectFile"
                            :doc-path="docURL"
                            :files.sync="mainData.authorization.authTakeEffectFile"
                            :tips="'根据相关管理要求，报告正文请线下加盖印章'"
                            disabled/>
                      </div>
                      <div v-else style="font-size: 15px">无</div>
                    </td>
                  </tr>
                  </tbody>
                  <tbody v-if="(this.parseCompliance === '是' &&
                  (this.queryType === 'toRead' || this.queryType === 'toDeal' )) || this.isRisk === '是'">
                  <tr>
                    <th class="th_label_approval" colspan="3">风控清单</th>
                    <td class="td_value_approval" colspan="21" style="height: 100%">
                      <uploadDoc
                          v-model="mainData.complianceFiles"
                          :disabled="isParseCompliance"
                          :doc-path="docURL"
                          :is-multiple="true"
                          :files.sync="mainData.complianceFiles"
                          :show-edit="!isParseCompliance"
                          @afterSuccess="uploadCompliance"
                      />
                    </td>
                  </tr>
                  <tr>
                    <th class="th_label_approval" colspan="3">业务负责人考核意见</th>
                    <td class="td_value_approval" colspan="21" v-if="!isParseCompliance">
                      <el-input
                          v-model="mainData.businessLeaders"
                          :autosize="{ minRows: 3, maxRows: 6 }"
                          maxlength="1000"
                          placeholder="请输入内容"
                          show-word-limit
                          type="textarea"/>
                    </td>
                    <td class="td_value_approval" colspan="21" v-else>
                      <div v-if="mainData.businessLeaders">{{ mainData.businessLeaders }}</div>
                      <div v-else style="font-size: 15px">无</div>
                    </td>
                  </tr>
                  <tr>
                    <th class="th_label_approval" colspan="3">部门负责人考核意见</th>
                    <td class="td_value_approval" colspan="9" v-if="!isParseCompliance">
                      <el-input
                          v-model="mainData.departmentHeads"
                          :autosize="{ minRows: 3, maxRows: 6 }"
                          maxlength="1000"
                          placeholder="请输入内容"
                          show-word-limit
                          type="textarea"/>
                    </td>
                    <td class="td_value_approval" colspan="9" v-else>
                      <div v-if="mainData.departmentHeads">{{ mainData.departmentHeads }}</div>
                      <div v-else style="font-size: 15px">无</div>
                    </td>
                    <th class="th_label_approval" colspan="3">考核分数</th>
                    <td class="td_value_approval" colspan="9" v-if="!isParseCompliance">
                      <el-input-number v-model.number="mainData.assessmentScore"
                                       :controls="false"
                                       :max="10"
                                       :min="0"
                                       placeholder="请输入分数"
                                       :precision="1"
                                       @focus="utils.inputFocus"></el-input-number>
                    </td>
                    <td class="td_value_approval" colspan="9" v-else>
                      <div>{{ mainData.assessmentScore }}</div>
                    </td>
                  </tr>

                  </tbody>
                </table>
              </SimpleBoardTitleApproval>
              <!--审批历史 -->
              <SimpleBoardTitleApproval class="print-table-wrap" style="margin-top: 10px;" title="审查意见">
                <ProcessOpinion :proc-inst-id="this.obj.processInstanceId" :task-id="this.obj.taskId"
                                style="border: solid 1px #606266;" type-code="1"/>
                <div v-if="approvalIs && isParseElement">
                  <el-input
                      v-model="approvalOpinion"
                      :rows="2"
                      placeholder="请输入审批意见"
                      style="border-radius: 0 !important;"
                      type="textarea">
                  </el-input>
                </div>
              </SimpleBoardTitleApproval>

              <SimpleBoardTitleApproval class="leadership-opinions-section-wrap" style="margin-top: 10px;"
                                        title="领导意见">
                <div style="border: solid 1px #606266;overflow: hidden">
                  <ProcessOpinion :proc-inst-id="this.obj.processInstanceId" :task-id="this.obj.taskId" type-code="2"/>
                  <div v-if="approvalIs && isParseElementlg">
                    <el-input
                        v-model="approvalOpinion"
                        :rows="2"
                        placeholder="请输入审批意见"
                        style="border-radius: 0 !important;"
                        type="textarea">
                    </el-input>
                  </div>
                </div>
              </SimpleBoardTitleApproval>
            </el-form>
          </el-scrollbar>
        </el-card>
      </el-main>

      <Shortcut :detail-show="detailShow"
                :download-show="downloadShow" :print-show="printShow"
                @detailClick="detailClick"
                @downloadClick="downloadClick" @printClick="printClick"/>
    </el-container>

    <!-- AI合同审查弹窗 -->
    <el-dialog
      title="AI合同审查"
      :visible.sync="aiReviewDialogVisible"
      width="90%"
      :before-close="handleAIReviewClose"
      top="5vh">
      <AIContractReview
        v-if="aiReviewDialogVisible"
        :contract-files="mainData.textList"
        @close="handleAIReviewClose"/>
    </el-dialog>

  </FormWindow>
</template>

<script>
import FormWindow from '@/view/components/FormWindow/FormWindow.vue'
import generic from './ContractGeneric.vue'
import contractApi from '@/api/contract/bmContract';
import processApi from '@/api/_system/process'
import noticeApi from '@/api/_system/notice'
import Shortcut from '@/view/litigation/caseManage/caseExamine/Shortcut.vue'
import orgApi from '@/api/_system/org'
import {mapGetters} from "vuex"
import SimpleBoardTitleApproval from "@/view/components/SimpleBoard/SimpleBoardTitleApproval.vue"
import ProcessOpinion from '@/view/components/ProcessOpinion/ProcessOpinion.vue'
import doc from "@/api/_system/doc";
import uploadDoc from "@/view/components/UploadDoc/UploadDoc.vue";
import AIContractReview from './AIContractReview.vue';

export default {
  name: "ContractApprovalMain",
  inject: ['layout', 'mcpLayout', 'mcpDesignPage'],
  components: {uploadDoc, FormWindow, generic, Shortcut, SimpleBoardTitleApproval, ProcessOpinion, AIContractReview},
  computed: {
    ...mapGetters(['orgContext', 'currentFunctionId']),
    isView: function () {
      return this.dataState === this.utils.formState.VIEW
    },
    detailShow() {
      if (this.view === undefined) {
        return true
      }
      return this.view === 'new' || this.view === 'detail'
    },
    printShow() {
      if (this.view === undefined) {
        return true
      }
      return this.view === 'new' || this.view === 'detail'
    },
    downloadShow() {
      if (this.view === undefined) {
        return true
      }
      return this.view === 'new' || this.view === 'detail'
    },
    // 如果是从oa打开，则需需要加上这两个参数
    originOa() {
      let {origin, fullScreen} = this.$route.query
      return origin === 'oa' && fullScreen
    },
    isParseElement() {
      return this.parseElement === '部门意见'
    },
    isParseElementlg() {
      return this.parseElement === '领导意见'
    },
    isParseCompliance() {
      return this.parseCompliance !== '是' || this.queryType === 'haveDealt'
    },
  },
  watch: {},
  provide() {
    return {
      parentContract: this
    }
  },
  data() {
    return {
      htmlTitle: "PDF名称",
      docURL: '/contract', // 测试暂时的
      nowTime: "",
      className: '',
      create: '',
      hasValue: null,
      qpShow: false,
      qpWidth: null,
      type: null,
      approvalOpinion: '',
      parseElement: null,
      parseCompliance: null,
      approvalIs: false,
      view: 'new',
      functionId: null,//终止的时候要用，需要手动关闭
      dataState: null,//表单状态，新增、查看、编辑
      loading: true,//查看时，加载中的动画
      mainData: {},
      activity: null,//记录当前待办处于流程实例的哪个环节
      queryType: null,
      isRisk: null,
      obj: {//流程处理逻辑需要的各种参数
        taskId: null,
        processInstanceId: null,
        businessKey: null,
        title: null,
        functionName: '合同审批',
        functionCode: 'contract_approval_main',
        sid: null,
      },
      noticeParams: {},
      noticeData: {
        moduleName: '', // 模块名称
        dataId: '', // 数据ID
        url: '', // 地址
        title: '', // 地址
        params: {} // 其他参数
      },
      aiReviewDialogVisible: false, // AI合同审查弹窗显示状态
      currentNodeInfo: null, // 当前岗位职责信息
    }
  },
  created() {
    //因为是流程功能，知会、抄送，都需要按照流程抄送的配置打开，
    // 只是知会的需要更新消息表，抄送需要更新日志表
    //判断是知会还是抄送，可以根据param中的参数isNotice判断
    this.obj.functionName = '合同审批'
    const isNotice = this.$route.query.isNotice
    // isNotice在OA中打开会解析成Boolean，系统内会被转成字符"true"
    if (isNotice == true || isNotice == "true") {//知会
      if (this.$route.query.processInstanceId !== null && this.$route.query.processInstanceId !== undefined) {//保存的数据，获取不到实例ID，只有启动流程实例才能拿到
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
      }
      const read = this.$route.query.read
      const sid = this.$route.query.sid
      if (read == false || read == 'false') {
        noticeApi.read({sid: sid})
      }
    } else {//这里除了抄送会走，其他正常逻辑也会走，所以下面的参数判断了type === 'toRead'，即未读时，才会更新OA消息和日志记录
      this.obj.sid = this.$route.query.sid//消息表中的消息ID 日志表中的日志ID
      if (this.$route.query.processInstanceId !== null && this.$route.query.processInstanceId !== undefined) {//保存的数据，获取不到实例ID，只有启动流程实例才能拿到
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
      }
      const type = this.$route.query.type//获取消息状态类型，toRead-》未读，此时需要更新，haveRead-》已读，就不需要更新了
      this.queryType = this.$route.query.type
      if (type === 'toRead') {
        this.obj.pathname = window.location.pathname
        //返回url路径名（https://www.runoob.com/try/try.php?filename=tryjsref_loc_pathname，返回/try/try.php），判断是在法务系统打开还是在OA中打开
        //法务中打开会把相同流程实例的全部消息改为已读，所以是更新OA多条，OA中打开是只更新OA一条
        this.obj.title = this.mainData.contractName
        //更新OA需要参数processInstanceId, title, functionName, oldTaskId,
        processApi.finishOATask(this.obj)
      }
    }
    this.view = this.$route.query.view
    this.isRisk = this.$route.query.isRisk
    const dataSourceCode = this.$route.query.dataSourceCode
    if (dataSourceCode && dataSourceCode === '1') {
      this.loadData('view', this.$route.query.dataId)
    }

  },
  mounted() {
    //挂载完毕后，设置回调函数
    this.$emit('setCallBack', {
      beforeCallBack: this.beforeApproval,//点击办理或提交前的回调，效验必填控制
      afterCallBack: this.afterApproval,//点击办理弹框中再点击确定后的回调，审批完成后处理业务逻辑
      setTaskNodeInfo: this.setTaskNodeInfo,//挂载完毕后执行的回调，用于页面已进入需要处理的业务逻辑
      filterBtn: this.utils.filterBtn,
      beforeConfirmCallBack: this.beforeConfirmCallBack,
      beforeApprovalCb: this.beforeApprovalCb
    })

    // 获取当前岗位职责信息
    this.getCurrentNodeInfo()
  },
  methods: {
    //撤回转办
    beforeApprovalCb(code) {
      debugger
      if (code.type === "cancelTransfer") {
        
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
        this.obj.title = this.mainData.contractName
        this.obj.code = code === null ? '' : code
        this.obj.functionName = '合同审批'
        let pathname = window.location.pathname
        this.obj.functionCode = 'contract_approval_main'
        processApi.sendOATask(this.obj).then(res => {
          if (pathname === '/base/design_page') {
            this.mcpLayout.closeTab()
          } else {
            window.close()
          }
        })
      }

    },
    editIsShow() {
      return this.$route.query.type === "toDeal";
    },
    setTaskNodeInfo(event) {
      const customProperties = event.customProperties.find(item => item.key === 'FlowCustomPropertiesSettingsServiceImpl')
      if (customProperties !== null && customProperties !== undefined && customProperties.value) {
        this.parseElement = JSON.parse(customProperties.value)['name']
      }
      const propertiesSetting = event.customProperties.find(item => item.key === 'FlowPropertiesSettingOfRevenueServiceImpl')
      if (propertiesSetting !== null && propertiesSetting !== undefined && propertiesSetting.value) {
        this.parseCompliance = JSON.parse(propertiesSetting.value)['name']
      }
      console.log("部门意见：" + this.parseElement + "，合规节点：" + this.parseCompliance)
      //（toDeal-待办处理，haveDealt-已办查看，toRead-未读，haveRead-已读）
      const type = this.$route.query.type
      // 业务ID
      let id = this.$route.query.businessKey
      /**
       * 已保存查看详情时传的参不是businessKey是dataId
       * */
      if (id === null || id === undefined)
        id = this.$route.query.dataId
      //是否首环节（submitNode-首环节）
      this.activity = event.taskNodeType
      /*
      * 首环节
      *   1、businessKey-是null，说明是刚发起---执行init方法
      *   2、businessKey-有值
      *       1、回退处理--环节是submitNode   ---执行init
      *       2、已办查看--优先判断  haveDealt---执行load
      *       3、未读查看--优先判断  toRead   ---执行load
      *       4、已读查看--优先判断  haveRead ---执行load
      * */
      if (type === 'haveDealt' || type === 'toRead' || type === 'haveRead') {
        this.loadData(this.utils.formState.VIEW, id)
      } else { // 不是以上3种只能是待处理，只需要判断是否首环节即可
        if (event.taskNodeType === 'submitNode') {
          this.loadData(this.utils.formState.NEW, id)
        } else {
          this.loadData(this.utils.formState.VIEW, id)
        }
      }
    },
    loadData(dataState, dataId) {
      this.functionId = this.$route.query.functionId
      if (this.$route.query.create !== undefined && this.$route.query.create !== '')
        this.create = this.$route.query.create
      if (this.$route.query.view !== undefined && this.$route.query.view !== '')
        this.view = this.$route.query.view
      this.dataState = dataState
      if (this.$route.query.type !== undefined && this.$route.query.type !== '')
        this.type = this.$route.query.type
      contractApi.queryById({
        id: dataId
      }).then(res => {
        this.mainData = res.data.data
        if (res.data.data.authorization !== null) {
          this.hasValue = true
        }
        this.approvalOpinionIs()
      })
    },
    //提交前 校验必填，resolve--校验成功或者失败需要将结果返回
    beforeApproval(code, resolve) {
      const me = this
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.save().then(response => {
            this.mainData.title = this.mainData.contractName
            this.mainData.code = code === null ? '' : code
            this.mainData.functionName = '合同审批'
            this.$emit('submit-success', this.mainData, 'id')
            this.$emit('setCallBack', {
              variables: {formData: {...this.mainData}}
            })
            resolve({
              success: true,
              formData: {...this.mainData},
              approvalOpinion: this.approvalOpinion,
              approveMessage: "流程审批通过"
            })
          })
        } else {
          resolve(false)
          this.$nextTick(function () {
            document.querySelector(".is-error").scrollIntoView(false);
          })
          return false
        }
      })
    },
    beforeConfirmCallBack(data, resolve, reject) {
      const customProperties = data.nodeInfo.customProperties.find(item => item.key === 'FlowCustomPropertiesSettingsServiceImpl')
      if (customProperties && customProperties.value && JSON.parse(customProperties.value)['id'] === "1" && data.approvalFormData.comment == "") {
        // 消息按需求编辑 这只是个示例
        this.$confirm('未填写意见，请确认是否继续。', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          resolve()()
        }).catch(() => {
          reject()
        })
      } else {
        resolve()
      }
    },
    afterApproval(code, data) {
      // 回退到首环节，修改业务数据状态，修改任务标题
      console.log("code==" + code)
      console.log("data==" + data)
      this.obj.businessKey = this.mainData.id
      //获取参数，为后续操作准备，processInstanceId和taskId其实在created中赋值了，这里在赋值一次也行
      if (data != null && data.data != null) {
        this.obj.processInstanceId = data.data.id
      }
      if (this.obj.businessKey == null || this.obj.processInstanceId == null) {
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
      }

      //需要传参数到流程中，这里操作，不限于首环节传参数
      if (this.activity === 'submitNode') {
        contractApi.setParam(this.obj).then(response => {
          console.log("传值成功")
        })
      }
      // 将部分参数传给OA
      this.obj.title = this.mainData.contractName
      this.obj.code = code === null ? '' : code
      this.obj.functionName = '合同审批'
      //不是动态节点，给OA传待办
      if (code !== 'dynamic') {
        let loading = this.$loading({
          target: document.querySelector('.sg-page-wrap'),
          lock: false,
          text: '请稍后...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        processApi.sendOATask(this.obj).then(res => {
          loading.close()
          if (this.originOa) {
            window.close()
          }
          /*let pathname = window.location.pathname
          if(pathname.indexOf('/design_pages')!==-1){
            window.close()
          }*/
        })
      }
    },
    saveComplianceFiles() {
      return new Promise((resolve, reject) => {
        contractApi.saveComplianceFiles({
          files: this.mainData.complianceFiles,
          riskPsn:this.mainData.riskPsn,
          riskPsnCode:this.mainData.riskPsnCode,
          undertakingTime:this.mainData.undertakingTime,
          id: this.mainData.id
        }).then(response => {
          resolve(response)
        }).catch(error => {
          reject(error)
        })
      })
    },
    submit() {
      this.save().then(response => {
        if (response.data.data.state === 'fail') {
          this.$message({
            message: response.data.data.message,
            dangerouslyUseHTMLString: true,
            showClose: true,
            type: 'warning'
          });
        } else {
          this.$emit('submit-success', this.mainData, 'id')
          this.$message.success('保存成功!')
        }
      })
    },
    save() {
      return new Promise((resolve, reject) => {
        const me = this
        console.log("执行save")
        contractApi.save(this.mainData).then(response => {
          resolve(response)
        }).catch(error => {
          reject(error)
        })
      })
    },
    stopClick() {
      this.$confirm('您确定要终止当前流程吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let processInstanceId = this.$route.query.processInstanceId
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
        this.obj.title = this.mainData.contractName
        this.obj.functionName = '合同审批'
        this.obj.code = 'stop'
        let pathname = window.location.pathname
        new Promise((resolve, reject) => {
          let processInstanceId = this.$route.query.processInstanceId
          processApi.end(processInstanceId).then(res => {
            resolve(res)
          })
        }).then(val => {
          if (val.data.code === 200) {
            this.obj.functionCode = 'contract_approval_main'
            processApi.sendOATask(this.obj).then(res => {
              if (pathname === '/base/design_page') {
                this.mcpLayout.closeTab()
              } else {
                window.close()
              }
            })
          }
        })
      }).catch(() => {
        this.$message.info('已取消删除!')
      })
    },
    finishClick() {
      this.$confirm('您确定要结束当前流程吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let processInstanceId = this.$route.query.processInstanceId
        this.obj.processInstanceId = this.$route.query.processInstanceId
        this.obj.taskId = this.$route.query.taskId
        this.obj.title = this.mainData.contractName
        this.obj.functionName = '合同审批'
        this.obj.code = 'pass'
        let pathname = window.location.pathname
        new Promise((resolve, reject) => {
          processApi.move({
            proInstId: this.$route.query.processInstanceId,
            taskId: this.$route.query.taskId
          }).then(res => {
            resolve(res)
          })
        }).then(val => {
          if (val.status === 200) {
            this.obj.functionCode = 'contract_approval_main'
            processApi.sendOATask(this.obj).then(res => {
              if (pathname === '/base/design_page') {
                this.mcpLayout.closeTab()
              } else {
                window.close()
              }
            })
          }
        })
      }).catch(() => {
        this.$message.info('已取消删除!')
      })
    },
    detailClick() {
      let row = this.mainData
      const tabId = this.utils.createUUID();
      this.layout.openNewTab(
          "合同详情",
          "contract_approval_main_detail",
          "contract_approval_main_detail",
          tabId,
          {
            functionId: "contract_approval_main_detail," + tabId,
            ...this.utils.routeState.VIEW(row.id)
          }
      )
    },
    printClick() {
      this.$print(this.$refs.dataForm)
    },
    downloadClick() {
      // 初始化一个空数组来存储合并后的结果
      let mergedArray = [];
      // 使用forEach()方法循环遍历列表并合并JSON数组
      if (this.mainData.textList.length > 0) {
        this.mainData.textList.forEach(item => {
          mergedArray = mergedArray.concat(JSON.parse(item.attachment));
        });
      }
      // if (this.mainData.complianceFiles) {
      //   mergedArray = mergedArray.concat(JSON.parse(this.mainData.complianceFiles));
      // }
      if (this.mainData.contractFiles) {
        mergedArray = mergedArray.concat(JSON.parse(this.mainData.contractFiles));
      }

      if (this.mainData.authorization && this.mainData.authorization.authTakeEffectFile) {
        mergedArray = mergedArray.concat(JSON.parse(this.mainData.authorization.authTakeEffectFile));
      }
      console.log("附件合并的结果")
      console.log(mergedArray)
      //下载全部的文件
      const loading = this.$loading({
        lock: true,
        text: "文件下载中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });
      doc.fileDown(mergedArray).then(response => {
        if (response) {
          const blob = response.data;
          const fileName = this.mainData.contractName + ".zip";
          if ("download" in document.createElement("a")) {
            // 非IE下载
            const elink = document.createElement("a");
            elink.download = fileName;
            elink.style.display = "none";
            elink.href = URL.createObjectURL(blob);
            document.body.appendChild(elink);
            elink.click();
            URL.revokeObjectURL(elink.href); // 释放URL 对象
            document.body.removeChild(elink);
          } else {
            // IE10+下载
            navigator.msSaveBlob(blob, fileName);
          }
        }
        loading.close();
      }).catch(res => {
        loading.close();
      });
    },
    downloadPDF() {
      this.htmlTitle = this.mainData.contractName
      this.ExportSavePdf(this.htmlTitle, this.nowTime)
    },
    approvalOpinionIs() {
      orgApi.roleCheck({orgId: this.orgContext.currentOrgId, roleName: 'LDYJ'}).then(res => {
        console.log('approvalOpinionIs：', res)
        this.approvalIs = !(this.type !== 'toDeal' || res.data.data === false)
      })
    },
    uploadCompliance() {
      if (this.mainData.riskPsn === undefined || this.mainData.riskPsn == null) {
        this.mainData.riskPsn = this.orgContext.currentPsnName
        this.mainData.riskPsnCode = this.orgContext.currentPsnId
        this.mainData.undertakingTime = new Date()
      }
      this.saveComplianceFiles()
    },
    // AI合同审查相关方法
    openAIContractReview() {
      this.aiReviewDialogVisible = true
    },
    handleAIReviewClose() {
      this.aiReviewDialogVisible = false
    },
    // 获取岗位职责信息
    getCurrentNodeInfo() {
      // 如果有ID参数，说明是查看已有数据，需要调用queryById接
      if (this.$route.query.businessKey) {
        contractApi.queryById({
        id:this.$route.query.businessKey
      }).then(res => {
          console.log('queryById接口返回数据:', res);
          // 根据实际返回的数据结构，currentNodes在res.data.currentNodes中
          if (res && res.data && res.data.currentNodes && res.data.currentNodes.length > 0) {
            const currentNode = res.currentNodes[0];
            console.log('当前节点信息:', currentNode);

            // 优先使用name字段，如果没有则使用nameId映射
            if (currentNode.name) {
              this.currentNodeInfo = currentNode.name;
              console.log('使用name字段:', currentNode.name);
            } else if (currentNode.nameId) {
              this.currentNodeInfo = this.getNodeNameByNodeId(currentNode.nameId);
              console.log('使用nameId映射:', currentNode.nameId, '->', this.currentNodeInfo);
            } else if (currentNode.nodeId) {
              this.currentNodeInfo = this.getNodeNameByNodeId(currentNode.nodeId);
              console.log('使用nodeId映射:', currentNode.nodeId, '->', this.currentNodeInfo);
            }

            console.log('最终设置的岗位职责信息:', this.currentNodeInfo);
          } else {
            console.log('未找到currentNodes数据，检查数据结构:', res);
          }
        }).catch(error => {
          console.error('获取当前节点信息失败:', error);
        });
      }
    },
    // 根据nodeId获取岗位名称
    getNodeNameByNodeId(nodeId) {
      const nodeMap = {
        'Activity_1rmp80v': '业务部门负责人',
        'Activity_1yc9eu3': '运改部审核',
        'Activity_1upb5zy': '税务审核',
        'Activity_0hgi73c': '资金审核',
        'Activity_0no6qkt': '财务部部长',
        'Activity_1qs8r6i': '法务部风控',
        'Activity_1lee3nx': '办公室',
        'Activity_1umzmjb': '公司领导',
        'Activity_0wn3tir': '返回经办人',
        'Activity_0y3xjh6': '法务承办人',
        'Activity_1e2ebp6': '合同专业负责人',
        'Activity_0pdswu8': '法务部部长',
        'Activity_0pz4x4e': '首席合规官',
        'Activity_1r1du0j': '三级审批',
        'Activity_0u0241c': '三级审批'
      };

      return nodeMap[nodeId] || `未知岗位(${nodeId})`;
    }
  },
}
</script>

<style scoped>
.mylabel .el-form-item .el-form-item__label {
  line-height: 15px;
}

</style>

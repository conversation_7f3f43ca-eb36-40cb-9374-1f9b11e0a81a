{"name": "erp-web", "version": "0.1.0", "private": true, "scripts": {"serve": "npx --max_old_space_size=4096  vue-cli-service serve", "build": "vue-cli-service build", "test:unit": "vue-cli-service test:unit"}, "dependencies": {"@babel/plugin-proposal-optional-chaining": "^7.14.5", "@jiaminghi/data-view": "^2.10.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "af-table-column": "^1.0.2", "ant-design-vue": "^1.7.8", "avue-plugin-map": "^0.0.7", "avue-plugin-ueditor": "^0.0.10", "axios": "^0.18.0", "babel-polyfill": "^6.26.0", "bpmn-js": "^9.2.0", "bpmnlint": "^7.8.0", "codemirror": "^5.49.2", "core-js": "^2.6.5", "default-passive-events": "^2.0.0", "diagram-js-minimap": "^2.1.1", "echarts": "^5.1.1", "el-table-infinite-scroll": "^1.0.10", "element-ui": "^2.15.10", "es6-promise": "^4.2.8", "fast-deep-equal": "^3.1.3", "file-saver": "^2.0.5", "font-awesome": "^4.7.0", "from": "^0.1.7", "html2canvas": "^1.4.1", "import": "^0.0.6", "js-cookie": "^2.2.0", "jspdf": "^2.5.1", "mcp2": "^3.0.4", "moment": "^2.29.1", "node-sass": "^4.13.0", "showdown": "^2.1.0", "uuid": "^3.3.3", "v-jsoneditor": "^1.4.5", "vue": "^2.6.6", "vue-awesome": "^4.5.0", "vue-draggable-resizable": "^2.0.0-rc1", "vue-element-extends": "^1.2.20", "vue-grid-layout": "^2.3.12", "vue-i18n": "^8.10.0", "vue-quill-editor": "^3.0.6", "vue-router": "^3.0.1", "vue2-org-tree": "^1.3.4", "vuedraggable": "^2.24.3", "vuex": "^3.0.1", "vxe-table": "^2.7.24", "vxe-table-plugin-element": "^1.6.0", "xcrud": "^0.4.19", "xe-utils": "^2.3.0", "xlsx": "^0.16.9"}, "devDependencies": {"@babel/plugin-transform-modules-umd": "^7.9.0", "@vue/cli-plugin-babel": "^3.5.0", "@vue/cli-plugin-unit-jest": "^3.5.0", "@vue/cli-service": "^3.5.0", "@vue/test-utils": "1.0.0-beta.29", "babel-core": "7.0.0-bridge.0", "babel-jest": "^23.6.0", "compression-webpack-plugin": "^6.1.1", "sass-loader": "^7.1.0", "script-loader": "^0.7.2", "vue-template-compiler": "^2.5.21"}}